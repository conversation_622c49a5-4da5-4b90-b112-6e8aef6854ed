from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from app.models import Report, Booking, Room, Customer, Payment, Review, db
from app.routes.auth import login_required
from datetime import datetime, date, timedelta
from sqlalchemy import func, extract
import json
import io
import csv

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/reports')
@login_required
def view_reports():
    """عرض التقارير"""
    reports = Report.query.filter_by(generated_by=session['user_id']).order_by(Report.created_at.desc()).all()
    return render_template('reports/list.html', reports=reports)

@reports_bp.route('/reports/generate', methods=['GET', 'POST'])
@login_required
def generate_report():
    """إنشاء تقرير جديد"""
    if request.method == 'POST':
        report_type = request.form['report_type']
        name = request.form['name']
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        
        parameters = {
            'start_date': start_date,
            'end_date': end_date,
            'filters': request.form.to_dict()
        }
        
        new_report = Report(
            name=name,
            report_type=report_type,
            parameters=json.dumps(parameters),
            generated_by=session['user_id']
        )
        
        db.session.add(new_report)
        db.session.commit()
        
        # Generate the actual report
        try:
            report_data = generate_report_data(report_type, parameters)
            new_report.status = 'completed'
            db.session.commit()
            flash('تم إنشاء التقرير بنجاح!', 'success')
            return redirect(url_for('reports.view_report', report_id=new_report.id))
        except Exception as e:
            new_report.status = 'failed'
            db.session.commit()
            flash(f'فشل في إنشاء التقرير: {str(e)}', 'danger')
    
    return render_template('reports/generate.html')

@reports_bp.route('/reports/view/<int:report_id>')
@login_required
def view_report(report_id):
    """عرض تقرير محدد"""
    report = Report.query.get_or_404(report_id)
    
    if report.generated_by != session['user_id'] and session.get('role') != 'admin':
        flash('ليس لديك صلاحية لعرض هذا التقرير', 'danger')
        return redirect(url_for('reports.view_reports'))
    
    parameters = json.loads(report.parameters)
    report_data = generate_report_data(report.report_type, parameters)
    
    return render_template('reports/view.html', report=report, data=report_data)

@reports_bp.route('/reports/financial')
@login_required
def financial_report():
    """تقرير مالي"""
    start_date = request.args.get('start_date', (date.today() - timedelta(days=30)).isoformat())
    end_date = request.args.get('end_date', date.today().isoformat())
    
    # إجمالي الإيرادات
    total_revenue = db.session.query(func.sum(Payment.amount)).filter(
        Payment.payment_status == 'completed',
        Payment.payment_date >= start_date,
        Payment.payment_date <= end_date
    ).scalar() or 0
    
    # الإيرادات حسب طريقة الدفع
    payment_methods = db.session.query(
        Payment.payment_method,
        func.sum(Payment.amount).label('total')
    ).filter(
        Payment.payment_status == 'completed',
        Payment.payment_date >= start_date,
        Payment.payment_date <= end_date
    ).group_by(Payment.payment_method).all()
    
    # الإيرادات اليومية
    daily_revenue = db.session.query(
        func.date(Payment.payment_date).label('date'),
        func.sum(Payment.amount).label('total')
    ).filter(
        Payment.payment_status == 'completed',
        Payment.payment_date >= start_date,
        Payment.payment_date <= end_date
    ).group_by(func.date(Payment.payment_date)).all()
    
    data = {
        'total_revenue': total_revenue,
        'payment_methods': payment_methods,
        'daily_revenue': daily_revenue,
        'start_date': start_date,
        'end_date': end_date
    }
    
    return render_template('reports/financial.html', data=data)

@reports_bp.route('/reports/occupancy')
@login_required
def occupancy_report():
    """تقرير الإشغال"""
    start_date = request.args.get('start_date', (date.today() - timedelta(days=30)).isoformat())
    end_date = request.args.get('end_date', date.today().isoformat())
    
    # معدل الإشغال
    total_rooms = Room.query.count()
    
    # الحجوزات في الفترة المحددة
    bookings = Booking.query.filter(
        Booking.check_in_date <= end_date,
        Booking.check_out_date >= start_date,
        Booking.status.in_(['confirmed', 'checked_in', 'checked_out'])
    ).all()
    
    # حساب معدل الإشغال اليومي
    occupancy_data = []
    current_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    while current_date <= end_date_obj:
        occupied_rooms = 0
        for booking in bookings:
            if booking.check_in_date <= current_date < booking.check_out_date:
                occupied_rooms += 1
        
        occupancy_rate = (occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0
        occupancy_data.append({
            'date': current_date.isoformat(),
            'occupied_rooms': occupied_rooms,
            'total_rooms': total_rooms,
            'occupancy_rate': round(occupancy_rate, 2)
        })
        current_date += timedelta(days=1)
    
    # الإشغال حسب نوع الغرفة
    room_type_occupancy = db.session.query(
        Room.room_type,
        func.count(Booking.id).label('bookings_count')
    ).join(Booking).filter(
        Booking.check_in_date >= start_date,
        Booking.check_out_date <= end_date,
        Booking.status.in_(['confirmed', 'checked_in', 'checked_out'])
    ).group_by(Room.room_type).all()
    
    data = {
        'occupancy_data': occupancy_data,
        'room_type_occupancy': room_type_occupancy,
        'total_rooms': total_rooms,
        'start_date': start_date,
        'end_date': end_date
    }
    
    return render_template('reports/occupancy.html', data=data)

@reports_bp.route('/reports/customer')
@login_required
def customer_report():
    """تقرير العملاء"""
    # أفضل العملاء (حسب عدد الحجوزات)
    top_customers = db.session.query(
        Customer.name,
        Customer.email,
        func.count(Booking.id).label('booking_count'),
        func.sum(Booking.total_price).label('total_spent')
    ).join(Booking).group_by(Customer.id).order_by(
        func.count(Booking.id).desc()
    ).limit(10).all()
    
    # إحصائيات العملاء
    total_customers = Customer.query.count()
    customers_with_bookings = db.session.query(func.count(func.distinct(Booking.customer_id))).scalar()
    
    # العملاء الجدد (آخر 30 يوم)
    thirty_days_ago = date.today() - timedelta(days=30)
    new_customers = Customer.query.filter(Customer.created_at >= thirty_days_ago).count()
    
    data = {
        'top_customers': top_customers,
        'total_customers': total_customers,
        'customers_with_bookings': customers_with_bookings,
        'new_customers': new_customers
    }
    
    return render_template('reports/customer.html', data=data)

@reports_bp.route('/reports/reviews')
@login_required
def reviews_report():
    """تقرير التقييمات"""
    # متوسط التقييمات
    avg_rating = db.session.query(func.avg(Review.rating)).scalar() or 0
    avg_room_rating = db.session.query(func.avg(Review.room_rating)).scalar() or 0
    avg_service_rating = db.session.query(func.avg(Review.service_rating)).scalar() or 0
    avg_cleanliness_rating = db.session.query(func.avg(Review.cleanliness_rating)).scalar() or 0
    
    # توزيع التقييمات
    rating_distribution = db.session.query(
        Review.rating,
        func.count(Review.id).label('count')
    ).group_by(Review.rating).all()
    
    # أحدث التقييمات
    recent_reviews = Review.query.order_by(Review.created_at.desc()).limit(10).all()
    
    data = {
        'avg_rating': round(avg_rating, 2),
        'avg_room_rating': round(avg_room_rating, 2),
        'avg_service_rating': round(avg_service_rating, 2),
        'avg_cleanliness_rating': round(avg_cleanliness_rating, 2),
        'rating_distribution': rating_distribution,
        'recent_reviews': recent_reviews
    }
    
    return render_template('reports/reviews.html', data=data)

@reports_bp.route('/reports/export/<int:report_id>')
@login_required
def export_report(report_id):
    """تصدير التقرير كـ CSV"""
    report = Report.query.get_or_404(report_id)
    
    if report.generated_by != session['user_id'] and session.get('role') != 'admin':
        flash('ليس لديك صلاحية لتصدير هذا التقرير', 'danger')
        return redirect(url_for('reports.view_reports'))
    
    parameters = json.loads(report.parameters)
    report_data = generate_report_data(report.report_type, parameters)
    
    # إنشاء ملف CSV
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة البيانات حسب نوع التقرير
    if report.report_type == 'financial':
        writer.writerow(['التاريخ', 'المبلغ', 'طريقة الدفع'])
        # إضافة البيانات...
    
    output.seek(0)
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'{report.name}.csv'
    )

def generate_report_data(report_type, parameters):
    """إنشاء بيانات التقرير"""
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')
    
    if report_type == 'financial':
        return generate_financial_data(start_date, end_date)
    elif report_type == 'occupancy':
        return generate_occupancy_data(start_date, end_date)
    elif report_type == 'customer':
        return generate_customer_data(start_date, end_date)
    else:
        return {}

def generate_financial_data(start_date, end_date):
    """إنشاء بيانات التقرير المالي"""
    # تنفيذ منطق التقرير المالي
    return {}

def generate_occupancy_data(start_date, end_date):
    """إنشاء بيانات تقرير الإشغال"""
    # تنفيذ منطق تقرير الإشغال
    return {}

def generate_customer_data(start_date, end_date):
    """إنشاء بيانات تقرير العملاء"""
    # تنفيذ منطق تقرير العملاء
    return {}
