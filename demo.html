<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الفندق - عرض توضيحي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .demo-container {
            background: white;
            margin: 20px auto;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 1200px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .feature-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .installation-step {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">
                <i class="fas fa-hotel me-3"></i>
                نظام إدارة الفندق
            </h1>
            <p class="lead text-muted">نظام شامل لإدارة الفنادق مطور بـ Python و Flask</p>
        </div>

        <!-- Installation Status -->
        <div class="alert alert-warning text-center" role="alert">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>Python غير مثبت</h4>
            <p class="mb-0">يجب تثبيت Python أولاً لتشغيل النظام</p>
        </div>

        <!-- Quick Installation -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card feature-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            تثبيت سريع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="installation-step">
                                    <h6><i class="fas fa-store me-2"></i>الطريقة الأولى: Microsoft Store</h6>
                                    <p class="small mb-2">الأسهل والأسرع</p>
                                    <button class="btn btn-primary btn-sm" onclick="openMicrosoftStore()">
                                        فتح Microsoft Store
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="installation-step">
                                    <h6><i class="fas fa-globe me-2"></i>الطريقة الثانية: python.org</h6>
                                    <p class="small mb-2">التحميل الرسمي</p>
                                    <button class="btn btn-success btn-sm" onclick="openPythonOrg()">
                                        فتح python.org
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="installation-step">
                                    <h6><i class="fas fa-terminal me-2"></i>الطريقة الثالثة: PowerShell</h6>
                                    <p class="small mb-2">تثبيت تلقائي</p>
                                    <div class="code-block small">
                                        .\start.bat
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Demo -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-center mb-4">
                    <i class="fas fa-star me-2"></i>
                    ميزات النظام
                </h3>
            </div>
            
            <!-- Stats Cards -->
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-primary">
                        <i class="fas fa-bed"></i>
                    </div>
                    <div class="stat-number">25</div>
                    <div class="stat-label">إجمالي الغرف</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-success">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-number">18</div>
                    <div class="stat-label">الغرف المتاحة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-warning">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">7</div>
                    <div class="stat-label">الغرف المحجوزة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-info">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-number">15,750</div>
                    <div class="stat-label">الإيرادات (ريال)</div>
                </div>
            </div>
        </div>

        <!-- Feature Cards -->
        <div class="row">
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <i class="fas fa-bed fa-3x text-primary mb-3"></i>
                        <h5>إدارة الغرف</h5>
                        <p class="text-muted">إضافة وتعديل وحذف الغرف مع تتبع التوفر</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
                        <h5>نظام الحجوزات</h5>
                        <p class="text-muted">حجز ذكي مع التحقق من التوفر والتواريخ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-3x text-warning mb-3"></i>
                        <h5>إدارة العملاء</h5>
                        <p class="text-muted">قاعدة بيانات شاملة للعملاء وتاريخ الحجوزات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <i class="fas fa-concierge-bell fa-3x text-info mb-3"></i>
                        <h5>الخدمات الإضافية</h5>
                        <p class="text-muted">إدارة خدمات الطعام والسبا والنقل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-bar fa-3x text-danger mb-3"></i>
                        <h5>التقارير المفصلة</h5>
                        <p class="text-muted">تقارير مالية وإحصائيات مع مخططات بيانية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <i class="fas fa-bell fa-3x text-secondary mb-3"></i>
                        <h5>نظام الإشعارات</h5>
                        <p class="text-muted">تنبيهات ذكية للحجوزات والصيانة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Installation Instructions -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card feature-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-rocket me-2"></i>
                            خطوات التشغيل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>بعد تثبيت Python:</h6>
                                <div class="code-block">
                                    # في PowerShell أو Command Prompt<br>
                                    .\start.bat
                                </div>
                                <p class="small text-muted">أو انقر نقراً مزدوجاً على start.bat</p>
                            </div>
                            <div class="col-md-6">
                                <h6>أو تشغيل يدوي:</h6>
                                <div class="code-block">
                                    pip install Flask Flask-SQLAlchemy<br>
                                    python app_simple.py
                                </div>
                                <p class="small text-muted">ثم افتح: http://localhost:5000</p>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <strong>بيانات تسجيل الدخول:</strong> admin / admin123
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-5 pt-4 border-top">
            <p class="text-muted">
                <i class="fas fa-heart text-danger me-1"></i>
                تم تطوير هذا النظام بـ Python و Flask لخدمة صناعة الضيافة
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openMicrosoftStore() {
            window.open('ms-windows-store://pdp/?ProductId=9NRWMJP3717K', '_blank');
        }
        
        function openPythonOrg() {
            window.open('https://www.python.org/downloads/', '_blank');
        }
        
        // Animate stats on load
        document.addEventListener('DOMContentLoaded', function() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent.replace(',', ''));
                let currentValue = 0;
                const increment = finalValue / 50;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        stat.textContent = finalValue.toLocaleString();
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(currentValue).toLocaleString();
                    }
                }, 30);
            });
        });
    </script>
</body>
</html>
