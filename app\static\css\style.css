/* Hotel Management System CSS */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #ecf0f1;
    --dark-text: #2c3e50;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    color: var(--dark-text);
    direction: rtl;
    text-align: right;
}

/* Header Styles */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: white !important;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    margin-top: 20px;
    min-height: calc(100vh - 200px);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px 15px 0 0 !important;
    font-weight: bold;
}

/* Dashboard Stats */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.stat-card.rooms { color: var(--secondary-color); }
.stat-card.bookings { color: var(--success-color); }
.stat-card.income { color: var(--warning-color); }
.stat-card.customers { color: var(--danger-color); }

/* Tables */
.table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #ecf0f1;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #ecf0f1;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 8px;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.login-card {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h2 {
    color: var(--primary-color);
    font-weight: bold;
}

.login-header .icon {
    font-size: 4rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

/* Footer */
.footer {
    background: var(--primary-color);
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 50px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .navbar-nav {
        text-align: center;
    }

    .table-responsive {
        font-size: 0.9rem;
    }

    .btn {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Badges */
.status-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-confirmed { background: #d4edda; color: #155724; }
.status-checked-in { background: #cce5ff; color: #004085; }
.status-checked-out { background: #f8f9fa; color: #6c757d; }
.status-cancelled { background: #f8d7da; color: #721c24; }

/* Room Type Colors */
.room-single { border-left: 5px solid var(--secondary-color); }
.room-double { border-left: 5px solid var(--success-color); }
.room-suite { border-left: 5px solid var(--warning-color); }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}