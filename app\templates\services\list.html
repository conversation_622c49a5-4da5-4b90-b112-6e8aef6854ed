{% extends "base.html" %}

{% block title %}إدارة الخدمات - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-concierge-bell me-2"></i>
            إدارة الخدمات
        </h1>
        <a href="{{ url_for('services.add_service') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة خدمة جديدة
        </a>
    </div>

    <!-- Services Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="stat-number">{{ services|selectattr('category', 'equalto', 'food')|list|length }}</div>
                <div class="stat-label">خدمات الطعام</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-spa"></i>
                </div>
                <div class="stat-number">{{ services|selectattr('category', 'equalto', 'spa')|list|length }}</div>
                <div class="stat-label">خدمات السبا</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon text-warning">
                    <i class="fas fa-car"></i>
                </div>
                <div class="stat-number">{{ services|selectattr('category', 'equalto', 'transport')|list|length }}</div>
                <div class="stat-label">خدمات النقل</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div class="stat-number">{{ services|selectattr('category', 'equalto', 'entertainment')|list|length }}</div>
                <div class="stat-label">خدمات الترفيه</div>
            </div>
        </div>
    </div>

    <!-- Services Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الخدمات
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" class="form-control" id="search-input" placeholder="البحث في الخدمات...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if services %}
                <div class="table-responsive">
                    <table class="table table-hover" id="services-table">
                        <thead>
                            <tr>
                                <th>اسم الخدمة</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td>
                                    <strong>{{ service.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {% if service.category == 'food' %}
                                            <i class="fas fa-utensils me-1"></i>طعام
                                        {% elif service.category == 'spa' %}
                                            <i class="fas fa-spa me-1"></i>سبا
                                        {% elif service.category == 'transport' %}
                                            <i class="fas fa-car me-1"></i>نقل
                                        {% elif service.category == 'entertainment' %}
                                            <i class="fas fa-gamepad me-1"></i>ترفيه
                                        {% else %}
                                            {{ service.category }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <strong class="text-success">{{ service.price }} ريال</strong>
                                </td>
                                <td>
                                    <span class="text-muted">
                                        {{ service.description[:50] }}{% if service.description|length > 50 %}...{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if service.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('services.edit_service', service_id=service.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('services.delete_service', service_id=service.id) }}" 
                                              class="d-inline">
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger btn-delete"
                                                    data-item-name="{{ service.name }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد خدمات مسجلة</h5>
                    <p class="text-muted">ابدأ بإضافة خدمات جديدة للفندق</p>
                    <a href="{{ url_for('services.add_service') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة خدمة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Export Options -->
    {% if services %}
    <div class="card mt-4">
        <div class="card-body">
            <h6 class="card-title">
                <i class="fas fa-download me-2"></i>
                تصدير البيانات
            </h6>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToCSV('services-table', 'services.csv')">
                    <i class="fas fa-file-csv me-2"></i>
                    تصدير CSV
                </button>
                <button type="button" class="btn btn-outline-info" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_css %}
<style>
.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

@media print {
    .btn, .card-header .input-group, .card:last-child {
        display: none !important;
    }
}
</style>
{% endblock %}
{% endblock %}
