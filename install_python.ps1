# PowerShell script to install Python and run Hotel Management System
# سكريبت PowerShell لتثبيت Python وتشغيل نظام إدارة الفندق

Write-Host "🏨 Hotel Management System - Python Installer" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host ""

# Check if Python is already installed
Write-Host "🔍 Checking for Python..." -ForegroundColor Yellow

try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
        $pythonInstalled = $true
    } else {
        $pythonInstalled = $false
    }
} catch {
    $pythonInstalled = $false
}

if (-not $pythonInstalled) {
    Write-Host "❌ Python not found" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 Python installation options:" -ForegroundColor Yellow
    Write-Host "1. Download from Microsoft Store (Recommended)" -ForegroundColor White
    Write-Host "2. Download from python.org" -ForegroundColor White
    Write-Host "3. Try to install automatically" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Choose option (1-3)"
    
    switch ($choice) {
        "1" {
            Write-Host "🏪 Opening Microsoft Store..." -ForegroundColor Green
            Start-Process "ms-windows-store://pdp/?ProductId=9NRWMJP3717K"
            Write-Host "Please install Python from Microsoft Store and run this script again" -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit
        }
        "2" {
            Write-Host "🌐 Opening python.org..." -ForegroundColor Green
            Start-Process "https://www.python.org/downloads/"
            Write-Host "Please download and install Python from python.org and run this script again" -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit
        }
        "3" {
            Write-Host "🔄 Trying automatic installation..." -ForegroundColor Yellow
            try {
                # Try to install Python using winget
                winget install Python.Python.3.11
                Write-Host "✅ Python installed successfully!" -ForegroundColor Green
                Write-Host "Please restart PowerShell and run this script again" -ForegroundColor Yellow
                Read-Host "Press Enter to exit"
                exit
            } catch {
                Write-Host "❌ Automatic installation failed" -ForegroundColor Red
                Write-Host "Please install Python manually from python.org" -ForegroundColor Yellow
                Read-Host "Press Enter to exit"
                exit
            }
        }
        default {
            Write-Host "❌ Invalid choice" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit
        }
    }
}

# Install Flask and Flask-SQLAlchemy
Write-Host ""
Write-Host "📦 Installing Flask and Flask-SQLAlchemy..." -ForegroundColor Yellow

try {
    python -m pip install Flask Flask-SQLAlchemy --quiet
    Write-Host "✅ Flask packages installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install Flask packages" -ForegroundColor Red
    Write-Host "Trying alternative method..." -ForegroundColor Yellow
    
    try {
        pip install Flask Flask-SQLAlchemy
        Write-Host "✅ Flask packages installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Could not install Flask packages automatically" -ForegroundColor Red
        Write-Host "Please run: python -m pip install Flask Flask-SQLAlchemy" -ForegroundColor Yellow
        Read-Host "Press Enter to continue anyway"
    }
}

# Test Flask installation
Write-Host ""
Write-Host "🧪 Testing Flask installation..." -ForegroundColor Yellow

$testResult = python -c "
try:
    import flask
    import flask_sqlalchemy
    print('✅ Flask is working!')
except ImportError as e:
    print(f'❌ Flask test failed: {e}')
    exit(1)
" 2>$null

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Flask test passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Flask test failed" -ForegroundColor Red
    Write-Host "Will try to run simple version..." -ForegroundColor Yellow
}

# Run the application
Write-Host ""
Write-Host "🚀 Starting Hotel Management System..." -ForegroundColor Green
Write-Host "🌐 Open your browser and go to: http://localhost:5000" -ForegroundColor Cyan
Write-Host "👤 Login with: admin / admin123" -ForegroundColor Cyan
Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Try to run different versions
if (Test-Path "app_simple.py") {
    Write-Host "Running simple version..." -ForegroundColor Green
    python app_simple.py
} elseif (Test-Path "run.py") {
    Write-Host "Running full version..." -ForegroundColor Green
    python run.py
} else {
    Write-Host "❌ No application files found" -ForegroundColor Red
    Write-Host "Please make sure you're in the correct directory" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Application stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
