@echo off
chcp 65001 >nul
title Hotel Management System - Quick Install
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   🏨 Hotel Management System                 ║
echo ║                      Quick Installation                      ║
echo ║                   نظام إدارة الفندق - تثبيت سريع              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
echo [1/4] 🔍 Checking for Python...
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python is NOT installed
        echo.
        echo 📥 INSTALL PYTHON FIRST:
        echo.
        echo 🏪 Option 1: Microsoft Store (Recommended)
        echo    1. Press Win+S, search "Microsoft Store"
        echo    2. Search for "Python 3.11"
        echo    3. Click "Get" or "Install"
        echo.
        echo 🌐 Option 2: python.org
        echo    1. Go to: https://python.org/downloads/
        echo    2. Download Python 3.11+
        echo    3. ✅ CHECK "Add Python to PATH" during installation
        echo.
        set /p choice="Press 1 for Microsoft Store, 2 for python.org, or any key to exit: "
        
        if "%choice%"=="1" (
            echo 🏪 Opening Microsoft Store...
            start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
        )
        
        if "%choice%"=="2" (
            echo 🌐 Opening python.org...
            start https://www.python.org/downloads/
        )
        
        echo.
        echo ⚠️  After installing Python:
        echo    1. Restart this computer
        echo    2. Run this script again
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ Python found (using py command)
        set PYTHON_CMD=py
    )
) else (
    echo ✅ Python found
    set PYTHON_CMD=python
)

echo.
echo [2/4] 📦 Installing Flask packages...
echo Installing Flask...
%PYTHON_CMD% -m pip install Flask --quiet
if errorlevel 1 (
    echo ❌ Failed to install Flask
    goto :error
)
echo ✅ Flask installed

echo Installing Flask-SQLAlchemy...
%PYTHON_CMD% -m pip install Flask-SQLAlchemy --quiet
if errorlevel 1 (
    echo ❌ Failed to install Flask-SQLAlchemy
    goto :error
)
echo ✅ Flask-SQLAlchemy installed

echo Installing Werkzeug...
%PYTHON_CMD% -m pip install Werkzeug --quiet
if errorlevel 1 (
    echo ❌ Failed to install Werkzeug
    goto :error
)
echo ✅ Werkzeug installed

echo.
echo [3/4] 🧪 Testing imports...
%PYTHON_CMD% -c "import flask; print('✅ Flask import OK')" 2>nul
if errorlevel 1 (
    echo ❌ Flask import test failed
    goto :error
)

%PYTHON_CMD% -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy import OK')" 2>nul
if errorlevel 1 (
    echo ❌ Flask-SQLAlchemy import test failed
    goto :error
)

%PYTHON_CMD% -c "from werkzeug.security import generate_password_hash; print('✅ Werkzeug import OK')" 2>nul
if errorlevel 1 (
    echo ❌ Werkzeug import test failed
    goto :error
)

echo.
echo [4/4] 🚀 Starting Hotel Management System...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  🎉 Installation Successful!                                ║
echo ║                                                              ║
echo ║  🌐 Open your browser: http://localhost:5000                ║
echo ║  👤 Login: admin / admin123                                 ║
echo ║  🛑 Press Ctrl+C to stop the server                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

if exist "app_simple.py" (
    %PYTHON_CMD% app_simple.py
) else if exist "run.py" (
    %PYTHON_CMD% run.py
) else (
    echo ❌ Application files not found
    echo Please make sure you're in the correct directory
    pause
    exit /b 1
)

goto :end

:error
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  ❌ Installation Failed                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔧 Try these solutions:
echo.
echo 1. Run as Administrator:
echo    - Right-click this file
echo    - Select "Run as administrator"
echo.
echo 2. Manual installation:
echo    %PYTHON_CMD% -m pip install Flask Flask-SQLAlchemy
echo.
echo 3. Use --user flag:
echo    %PYTHON_CMD% -m pip install --user Flask Flask-SQLAlchemy
echo.
echo 4. Check internet connection
echo.
echo 5. Update pip:
echo    %PYTHON_CMD% -m pip install --upgrade pip
echo.
pause
exit /b 1

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  👋 Application Stopped                                      ║
echo ║                                                              ║
echo ║  📖 For help: Read SETUP_GUIDE.md                           ║
echo ║  🔄 To restart: Run this script again                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
pause
