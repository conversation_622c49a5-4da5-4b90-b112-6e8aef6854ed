<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    {% include 'base.html' %}
    <div class="container">
        <h1>Booking Management</h1>
        <a href="{{ url_for('bookings.create') }}" class="btn btn-primary">Create New Booking</a>
        <table class="table">
            <thead>
                <tr>
                    <th>Booking ID</th>
                    <th>Customer Name</th>
                    <th>Room Number</th>
                    <th>Check-in Date</th>
                    <th>Check-out Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for booking in bookings %}
                <tr>
                    <td>{{ booking.id }}</td>
                    <td>{{ booking.customer.name }}</td>
                    <td>{{ booking.room.number }}</td>
                    <td>{{ booking.check_in_date }}</td>
                    <td>{{ booking.check_out_date }}</td>
                    <td>{{ booking.status }}</td>
                    <td>
                        <a href="{{ url_for('bookings.edit', booking_id=booking.id) }}" class="btn btn-warning">Edit</a>
                        <a href="{{ url_for('bookings.delete', booking_id=booking.id) }}" class="btn btn-danger">Delete</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>