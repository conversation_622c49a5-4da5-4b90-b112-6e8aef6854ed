# Hotel Management System Environment Configuration
# نظام إدارة الفندق - إعدادات البيئة

# Flask Configuration
SECRET_KEY=your_secret_key_here_change_this_in_production
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///hotel.db
# For PostgreSQL use:
# DATABASE_URL=postgresql://username:password@localhost/hotel_db

# Application Settings
APP_NAME=Hotel Management System
APP_VERSION=1.0.0
TIMEZONE=Asia/Riyadh

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# File Upload Settings
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=app/static/uploads

# Security Settings
SESSION_COOKIE_SECURE=False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY=True
PERMANENT_SESSION_LIFETIME=3600  # 1 hour

# Pagination Settings
POSTS_PER_PAGE=20
BOOKINGS_PER_PAGE=25
CUSTOMERS_PER_PAGE=30

# Report Settings
REPORTS_FOLDER=app/static/reports
BACKUP_FOLDER=backups

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=False
ENABLE_PUSH_NOTIFICATIONS=True
NOTIFICATION_CHECK_INTERVAL=30  # seconds

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/hotel_management.log
