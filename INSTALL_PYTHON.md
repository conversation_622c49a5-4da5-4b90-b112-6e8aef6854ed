# 🐍 دليل تثبيت Python ونظام إدارة الفندق

## ❌ المشكلة الحالية
```
Import "flask_sqlalchemy" could not be resolved
Python was not found
```

## 🎯 الحل السريع

### الخطوة 1: تثبيت Python

#### الطريقة الأولى: Microsoft Store (الأسهل)
1. اضغط `Win + R`
2. اكتب: `ms-windows-store:`
3. ابحث عن "Python"
4. اختر "Python 3.11" أو أحدث
5. اضغط "Get" أو "تثبيت"

#### الطريقة الثانية: التحميل المباشر
1. اذهب إلى: https://python.org/downloads/
2. اضغط "Download Python 3.11.x"
3. شغل الملف المحمل
4. **مهم جداً:** تأكد من تحديد "Add Python to PATH"
5. اضغط "Install Now"

### الخطوة 2: التحقق من التثبيت
افتح Command Prompt أو PowerShell واكتب:
```bash
python --version
```
يجب أن ترى شيئاً مثل: `Python 3.11.x`

### الخطوة 3: تثبيت Flask
```bash
python -m pip install Flask Flask-SQLAlchemy
```

### الخطوة 4: تشغيل النظام
```bash
python app_simple.py
```

## 🚀 طرق التشغيل السريع

### الطريقة الأولى: ملف Batch
انقر نقراً مزدوجاً على:
```
start.bat
```

### الطريقة الثانية: PowerShell
```powershell
.\start.bat
```

### الطريقة الثالثة: Command Prompt
```cmd
start.bat
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "python is not recognized"
**الحل:**
1. أعد تثبيت Python مع تحديد "Add to PATH"
2. أو استخدم المسار الكامل:
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
```

### مشكلة: "pip is not recognized"
**الحل:**
```bash
python -m pip install Flask Flask-SQLAlchemy
```

### مشكلة: "Permission denied"
**الحل:**
1. شغل Command Prompt كمدير
2. أو استخدم:
```bash
python -m pip install --user Flask Flask-SQLAlchemy
```

## ✅ اختبار سريع
```bash
python -c "import flask; print('Flask OK')"
python -c "import flask_sqlalchemy; print('SQLAlchemy OK')"
```

## 🎉 بعد التثبيت الناجح

1. **افتح المتصفح على:** http://localhost:5000
2. **سجل دخول بـ:** admin / admin123
3. **استمتع بالنظام!**

## 📞 إذا استمرت المشاكل

1. أعد تشغيل الكمبيوتر بعد تثبيت Python
2. تأكد من إصدار Python 3.7+
3. جرب البيئة الافتراضية:
```bash
python -m venv hotel_env
hotel_env\Scripts\activate
pip install Flask Flask-SQLAlchemy
python app_simple.py
```

---
**💡 نصيحة:** ابدأ بـ Microsoft Store - الأسهل والأسرع!
