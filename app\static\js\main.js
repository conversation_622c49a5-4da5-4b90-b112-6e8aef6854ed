// Hotel Management System JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips and popovers
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Room availability checker
    const checkAvailabilityBtn = document.getElementById('check-availability');
    if (checkAvailabilityBtn) {
        checkAvailabilityBtn.addEventListener('click', checkRoomAvailability);
    }

    // Delete confirmation
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const itemName = this.dataset.itemName || 'هذا العنصر';
            if (confirm(`هل أنت متأكد من حذف ${itemName}؟`)) {
                // Submit the form or make the request
                const form = this.closest('form');
                if (form) {
                    form.submit();
                } else {
                    window.location.href = this.href;
                }
            }
        });
    });

    // Dynamic price calculation for bookings
    const checkInDate = document.getElementById('check_in_date');
    const checkOutDate = document.getElementById('check_out_date');
    const roomSelect = document.getElementById('room_id');
    const totalPriceDisplay = document.getElementById('total_price');

    if (checkInDate && checkOutDate && roomSelect && totalPriceDisplay) {
        [checkInDate, checkOutDate, roomSelect].forEach(element => {
            element.addEventListener('change', calculateTotalPrice);
        });
    }

    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    // Loading spinner for forms
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const form = this.closest('form');
            if (form && form.checkValidity()) {
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> جاري التحميل...';
                this.disabled = true;
            }
        });
    });

    // Initialize notifications
    initializeNotifications();

    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    }
});

// Room availability checker function
function checkRoomAvailability() {
    const checkInDate = document.getElementById('check_in_date').value;
    const checkOutDate = document.getElementById('check_out_date').value;

    if (!checkInDate || !checkOutDate) {
        alert('يرجى تحديد تواريخ الوصول والمغادرة');
        return;
    }

    const formData = new FormData();
    formData.append('check_in_date', checkInDate);
    formData.append('check_out_date', checkOutDate);

    fetch('/bookings/check_availability', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        displayAvailableRooms(data);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في التحقق من توفر الغرف');
    });
}

// Display available rooms
function displayAvailableRooms(rooms) {
    const container = document.getElementById('available-rooms');
    if (!container) return;

    container.innerHTML = '';

    if (rooms.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">لا توجد غرف متاحة في هذه التواريخ</div>';
        return;
    }

    const roomsHtml = rooms.map(room => `
        <div class="col-md-4 mb-3">
            <div class="card room-${room.room_type}">
                <div class="card-body">
                    <h5 class="card-title">غرفة ${room.room_number}</h5>
                    <p class="card-text">
                        <strong>النوع:</strong> ${getRoomTypeArabic(room.room_type)}<br>
                        <strong>السعر:</strong> ${room.price} ريال/ليلة<br>
                        <strong>السعة:</strong> ${room.max_occupancy} أشخاص
                    </p>
                    <button class="btn btn-primary btn-sm" onclick="selectRoom(${room.id})">
                        اختيار هذه الغرفة
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = `<div class="row">${roomsHtml}</div>`;
}

// Select room for booking
function selectRoom(roomId) {
    const roomSelect = document.getElementById('room_id');
    if (roomSelect) {
        roomSelect.value = roomId;
        calculateTotalPrice();

        // Scroll to booking form
        const bookingForm = document.getElementById('booking-form');
        if (bookingForm) {
            bookingForm.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// Calculate total price for booking
function calculateTotalPrice() {
    const checkInDate = document.getElementById('check_in_date');
    const checkOutDate = document.getElementById('check_out_date');
    const roomSelect = document.getElementById('room_id');
    const totalPriceDisplay = document.getElementById('total_price');

    if (!checkInDate.value || !checkOutDate.value || !roomSelect.value) {
        return;
    }

    const checkIn = new Date(checkInDate.value);
    const checkOut = new Date(checkOutDate.value);
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));

    if (nights <= 0) {
        totalPriceDisplay.textContent = 'تواريخ غير صحيحة';
        return;
    }

    const roomPrice = parseFloat(roomSelect.selectedOptions[0].dataset.price || 0);
    const totalPrice = nights * roomPrice;

    totalPriceDisplay.textContent = `${totalPrice} ريال (${nights} ليلة × ${roomPrice} ريال)`;
}

// Get room type in Arabic
function getRoomTypeArabic(roomType) {
    const types = {
        'single': 'مفردة',
        'double': 'مزدوجة',
        'suite': 'جناح'
    };
    return types[roomType] || roomType;
}

// Utility functions
function showLoading(element) {
    element.innerHTML = '<div class="spinner"></div>';
}

function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

// Notifications System
function initializeNotifications() {
    // Load initial notifications count
    updateNotificationCount();

    // Check for new notifications every 30 seconds
    setInterval(updateNotificationCount, 30000);

    // Load recent notifications when dropdown is opened
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    if (notificationsDropdown) {
        notificationsDropdown.addEventListener('click', loadRecentNotifications);
    }
}

function updateNotificationCount() {
    fetch('/api/notifications/unread_count')
        .then(response => response.json())
        .then(data => {
            const countElement = document.getElementById('notification-count');
            if (countElement) {
                if (data.count > 0) {
                    countElement.textContent = data.count;
                    countElement.style.display = 'block';
                } else {
                    countElement.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error updating notification count:', error));
}

function loadRecentNotifications() {
    fetch('/api/notifications/recent')
        .then(response => response.json())
        .then(data => {
            const notificationsList = document.getElementById('notifications-list');
            if (notificationsList && data.length > 0) {
                notificationsList.innerHTML = '';
                data.forEach(notification => {
                    const notificationItem = createNotificationItem(notification);
                    notificationsList.appendChild(notificationItem);
                });
            }
        })
        .catch(error => console.error('Error loading notifications:', error));
}

function createNotificationItem(notification) {
    const li = document.createElement('li');
    const priorityClass = notification.priority === 'high' ? 'text-danger' :
                         notification.priority === 'medium' ? 'text-warning' : 'text-info';

    li.innerHTML = `
        <a class="dropdown-item ${notification.is_read ? '' : 'fw-bold'}" href="/notifications/mark_read/${notification.id}">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="small ${priorityClass}">${notification.title}</div>
                    <div class="text-muted small">${notification.message.substring(0, 50)}...</div>
                </div>
                <div class="small text-muted">${formatDate(notification.created_at)}</div>
            </div>
        </a>
    `;

    return li;
}

// Charts and Analytics
function initializeCharts() {
    // Initialize dashboard charts
    initializeDashboardCharts();

    // Initialize reports charts
    initializeReportsCharts();
}

function initializeDashboardCharts() {
    // Occupancy Chart
    const occupancyChart = document.getElementById('occupancyChart');
    if (occupancyChart) {
        const ctx = occupancyChart.getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['غرف متاحة', 'غرف محجوزة'],
                datasets: [{
                    data: [
                        parseInt(occupancyChart.dataset.available) || 0,
                        parseInt(occupancyChart.dataset.booked) || 0
                    ],
                    backgroundColor: ['#28a745', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Revenue Chart
    const revenueChart = document.getElementById('revenueChart');
    if (revenueChart) {
        const ctx = revenueChart.getContext('2d');
        // Get data from data attributes or API
        fetch('/api/dashboard/revenue_data')
            .then(response => response.json())
            .then(data => {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            label: 'الإيرادات اليومية',
                            data: data.values,
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('Error loading revenue data:', error));
    }
}

function initializeReportsCharts() {
    // Financial Report Charts
    const financialChart = document.getElementById('financialChart');
    if (financialChart) {
        // Initialize financial charts
    }

    // Occupancy Report Charts
    const occupancyReportChart = document.getElementById('occupancyReportChart');
    if (occupancyReportChart) {
        // Initialize occupancy report charts
    }
}

// Advanced Features
function initializeCalendar() {
    const calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            events: '/api/bookings/calendar',
            eventClick: function(info) {
                // Handle event click
                showBookingDetails(info.event.id);
            }
        });
        calendar.render();
    }
}

function showBookingDetails(bookingId) {
    // Show booking details in modal
    fetch(`/api/bookings/${bookingId}`)
        .then(response => response.json())
        .then(data => {
            // Populate modal with booking data
            const modal = new bootstrap.Modal(document.getElementById('bookingModal'));
            modal.show();
        })
        .catch(error => console.error('Error loading booking details:', error));
}

// Export Functions
function exportToCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    if (!table) return;

    let csv = [];
    const rows = table.querySelectorAll('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');

        for (let j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }

        csv.push(row.join(','));
    }

    downloadCSV(csv.join('\n'), filename);
}

function downloadCSV(csv, filename) {
    const csvFile = new Blob([csv], { type: 'text/csv' });
    const downloadLink = document.createElement('a');

    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Print Functions
function printReport() {
    window.print();
}

// Advanced Search
function initializeAdvancedSearch() {
    const searchForm = document.getElementById('advanced-search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performAdvancedSearch();
        });
    }
}

function performAdvancedSearch() {
    const formData = new FormData(document.getElementById('advanced-search-form'));
    const searchParams = new URLSearchParams(formData);

    fetch(`/api/search?${searchParams}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => console.error('Error performing search:', error));
}

function displaySearchResults(results) {
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="alert alert-info">لا توجد نتائج</div>';
            return;
        }

        results.forEach(result => {
            const resultElement = createSearchResultElement(result);
            resultsContainer.appendChild(resultElement);
        });
    }
}

function createSearchResultElement(result) {
    const div = document.createElement('div');
    div.className = 'card mb-3';
    div.innerHTML = `
        <div class="card-body">
            <h5 class="card-title">${result.title}</h5>
            <p class="card-text">${result.description}</p>
            <a href="${result.url}" class="btn btn-primary">عرض التفاصيل</a>
        </div>
    `;
    return div;
}