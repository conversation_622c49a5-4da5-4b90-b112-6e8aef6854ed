{% extends "base.html" %}

{% block title %}التقرير المالي - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-chart-line me-2"></i>
            التقرير المالي
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-success" onclick="exportToCSV('financial-table', 'financial_report.csv')">
                <i class="fas fa-file-csv me-2"></i>
                تصدير CSV
            </button>
            <button type="button" class="btn btn-outline-info" onclick="printReport()">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="{{ data.start_date }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="{{ data.end_date }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Financial Summary -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card income">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number">{{ "{:,.2f}".format(data.total_revenue) }}</div>
                <div class="stat-label">إجمالي الإيرادات (ريال)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="stat-number">
                    {{ data.payment_methods|selectattr('0', 'equalto', 'card')|map(attribute='1')|sum or 0 }}
                </div>
                <div class="stat-label">مدفوعات البطاقة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon text-warning">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number">
                    {{ data.payment_methods|selectattr('0', 'equalto', 'cash')|map(attribute='1')|sum or 0 }}
                </div>
                <div class="stat-label">مدفوعات نقدية</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-university"></i>
                </div>
                <div class="stat-number">
                    {{ data.payment_methods|selectattr('0', 'equalto', 'transfer')|map(attribute='1')|sum or 0 }}
                </div>
                <div class="stat-label">تحويلات بنكية</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        الإيرادات اليومية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Payment Methods Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        طرق الدفع
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Revenue Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-table me-2"></i>
                تفاصيل الإيرادات اليومية
            </h5>
        </div>
        <div class="card-body">
            {% if data.daily_revenue %}
                <div class="table-responsive">
                    <table class="table table-hover" id="financial-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الإيرادات (ريال)</th>
                                <th>عدد المعاملات</th>
                                <th>متوسط المعاملة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for day in data.daily_revenue %}
                            <tr>
                                <td>{{ day.date }}</td>
                                <td class="text-success fw-bold">{{ "{:,.2f}".format(day.total) }}</td>
                                <td>{{ day.transactions or 0 }}</td>
                                <td>{{ "{:,.2f}".format(day.total / (day.transactions or 1)) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-primary">
                                <th>الإجمالي</th>
                                <th class="text-success">{{ "{:,.2f}".format(data.total_revenue) }}</th>
                                <th>{{ data.daily_revenue|sum(attribute='transactions') or 0 }}</th>
                                <th>{{ "{:,.2f}".format(data.total_revenue / (data.daily_revenue|length or 1)) }}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات مالية</h5>
                    <p class="text-muted">لا توجد معاملات مالية في الفترة المحددة</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Payment Methods Breakdown -->
    {% if data.payment_methods %}
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-credit-card me-2"></i>
                تفصيل طرق الدفع
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for method, total in data.payment_methods %}
                <div class="col-md-4 mb-3">
                    <div class="card border-start border-4 border-primary">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    {% if method == 'cash' %}
                                        <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                                    {% elif method == 'card' %}
                                        <i class="fas fa-credit-card fa-2x text-primary"></i>
                                    {% elif method == 'transfer' %}
                                        <i class="fas fa-university fa-2x text-info"></i>
                                    {% else %}
                                        <i class="fas fa-payment fa-2x text-secondary"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <h6 class="mb-1">
                                        {% if method == 'cash' %}
                                            نقدي
                                        {% elif method == 'card' %}
                                            بطاقة ائتمان
                                        {% elif method == 'transfer' %}
                                            تحويل بنكي
                                        {% else %}
                                            {{ method }}
                                        {% endif %}
                                    </h6>
                                    <h4 class="mb-0 text-success">{{ "{:,.2f}".format(total) }} ريال</h4>
                                    <small class="text-muted">
                                        {{ "{:.1f}".format((total / data.total_revenue * 100) if data.total_revenue > 0 else 0) }}% من الإجمالي
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<style>
@media print {
    .btn, .card:first-child, .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .stat-card {
        border: 1px solid #ddd !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [{% for day in data.daily_revenue %}'{{ day.date }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'الإيرادات اليومية',
                    data: [{% for day in data.daily_revenue %}{{ day.total }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('ar-SA') + ' ريال';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y.toLocaleString('ar-SA') + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }

    // Payment Methods Chart
    const paymentCtx = document.getElementById('paymentMethodsChart');
    if (paymentCtx) {
        new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for method, total in data.payment_methods %}
                    '{% if method == "cash" %}نقدي{% elif method == "card" %}بطاقة{% elif method == "transfer" %}تحويل{% else %}{{ method }}{% endif %}'
                    {% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [{% for method, total in data.payment_methods %}{{ total }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: ['#28a745', '#007bff', '#17a2b8', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed.toLocaleString('ar-SA') + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}
{% endblock %}
