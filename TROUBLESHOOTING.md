# 🔧 حل مشاكل نظام إدارة الفندق

## ❌ مشكلة: "Import flask could not be resolved"

### السبب:
Flask غير مثبت أو غير متوفر في بيئة Python الحالية.

### الحلول (جرب بالترتيب):

#### الحل الأول: التثبيت التلقائي
```bash
python quick_start.py
```

#### الحل الثاني: إصلاح الاستيراد
```bash
python fix_imports.py
```

#### الحل الثالث: التثبيت اليدوي
```bash
pip install Flask Flask-SQLAlchemy
```

#### الحل الرابع: استخدام python -m pip
```bash
python -m pip install Flask Flask-SQLAlchemy
```

#### الحل الخامس: تشغيل كمدير (Windows)
1. افتح Command Prompt كمدير (Run as Administrator)
2. نفذ: `pip install Flask Flask-SQLAlchemy`

#### الحل السادس: النسخة المبسطة
```bash
python app_simple.py
```

---

## ❌ مشكلة: "pip is not recognized"

### الحلول:

#### الحل الأول: استخدام python -m pip
```bash
python -m pip install Flask Flask-SQLAlchemy
```

#### الحل الثاني: تحديد مسار Python كاملاً
```bash
C:\Python39\python.exe -m pip install Flask Flask-SQLAlchemy
```

#### الحل الثالث: إعادة تثبيت Python
1. حمل Python من https://python.org
2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت

---

## ❌ مشكلة: "Permission denied"

### الحلول:

#### الحل الأول: التثبيت للمستخدم الحالي
```bash
pip install --user Flask Flask-SQLAlchemy
```

#### الحل الثاني: تشغيل كمدير
- Windows: افتح Command Prompt كمدير
- Linux/Mac: استخدم `sudo pip install Flask Flask-SQLAlchemy`

---

## ❌ مشكلة: "No module named 'flask_sqlalchemy'"

### الحلول:

#### الحل الأول: تثبيت Flask-SQLAlchemy
```bash
pip install Flask-SQLAlchemy
```

#### الحل الثاني: التحقق من التثبيت
```bash
python -c "import flask_sqlalchemy; print('OK')"
```

#### الحل الثالث: إعادة التثبيت
```bash
pip uninstall Flask-SQLAlchemy
pip install Flask-SQLAlchemy
```

---

## ❌ مشكلة: "Python is not recognized"

### الحلول:

#### الحل الأول: تحديد مسار Python
```bash
C:\Python39\python.exe app_simple.py
```

#### الحل الثاني: إضافة Python للـ PATH
1. ابحث عن "Environment Variables" في Windows
2. أضف مسار Python للـ PATH
3. أعد تشغيل Command Prompt

#### الحل الثالث: استخدام py (Windows)
```bash
py app_simple.py
```

---

## ❌ مشكلة: "Port 5000 is already in use"

### الحلول:

#### الحل الأول: إيقاف العملية المستخدمة للمنفذ
```bash
# Windows
netstat -ano | findstr :5000
taskkill /PID <PID_NUMBER> /F

# Linux/Mac
lsof -ti:5000 | xargs kill -9
```

#### الحل الثاني: استخدام منفذ آخر
عدل في ملف `app_simple.py` السطر الأخير:
```python
app.run(debug=True, host='0.0.0.0', port=8000)
```

---

## ❌ مشكلة: "Database is locked"

### الحلول:

#### الحل الأول: حذف ملف قاعدة البيانات
```bash
del hotel_simple.db
```

#### الحل الثاني: إعادة تشغيل التطبيق
```bash
python app_simple.py
```

---

## ❌ مشكلة: "Template not found"

### السبب:
التطبيق الكامل يحتاج ملفات HTML منفصلة.

### الحل:
استخدم النسخة المبسطة:
```bash
python app_simple.py
```

---

## ✅ اختبار سريع للتأكد من عمل Flask

```bash
python -c "
try:
    from flask import Flask
    from flask_sqlalchemy import SQLAlchemy
    print('✅ Flask is working!')
except ImportError as e:
    print(f'❌ Error: {e}')
"
```

---

## 🆘 إذا لم تنجح جميع الحلول

### الخيار الأول: استخدام البيئة الافتراضية
```bash
python -m venv hotel_env
hotel_env\Scripts\activate  # Windows
# أو
source hotel_env/bin/activate  # Linux/Mac

pip install Flask Flask-SQLAlchemy
python app_simple.py
```

### الخيار الثاني: إعادة تثبيت Python
1. حمل Python من https://python.org
2. تأكد من تحديد "Add Python to PATH"
3. أعد تشغيل الكمبيوتر
4. جرب مرة أخرى

### الخيار الثالث: استخدام Anaconda
1. حمل Anaconda من https://anaconda.com
2. افتح Anaconda Prompt
3. نفذ: `conda install flask flask-sqlalchemy`
4. نفذ: `python app_simple.py`

---

## 📞 طلب المساعدة

إذا استمرت المشاكل، أرسل المعلومات التالية:

1. **نظام التشغيل:**
   ```bash
   # Windows
   systeminfo | findstr "OS Name"
   
   # Linux/Mac
   uname -a
   ```

2. **إصدار Python:**
   ```bash
   python --version
   ```

3. **رسالة الخطأ كاملة**

4. **المكتبات المثبتة:**
   ```bash
   pip list
   ```

---

## 🎯 نصائح لتجنب المشاكل

1. **استخدم البيئة الافتراضية دائماً**
2. **تأكد من إصدار Python 3.7+**
3. **شغل Command Prompt كمدير عند التثبيت**
4. **تأكد من اتصال الإنترنت عند التثبيت**
5. **أعد تشغيل الكمبيوتر بعد تثبيت Python**
