<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Room Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    {% include 'base.html' %}
    <div class="container">
        <h1>Room Management</h1>
        <a href="{{ url_for('rooms.add') }}" class="btn btn-primary">Add Room</a>
        <table class="table">
            <thead>
                <tr>
                    <th>Room Number</th>
                    <th>Room Type</th>
                    <th>Price</th>
                    <th>Availability</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for room in rooms %}
                <tr>
                    <td>{{ room.number }}</td>
                    <td>{{ room.type }}</td>
                    <td>{{ room.price }}</td>
                    <td>{{ 'Available' if room.is_available else 'Booked' }}</td>
                    <td>
                        <a href="{{ url_for('rooms.edit', room_id=room.id) }}" class="btn btn-warning">Edit</a>
                        <a href="{{ url_for('rooms.delete', room_id=room.id) }}" class="btn btn-danger">Delete</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>