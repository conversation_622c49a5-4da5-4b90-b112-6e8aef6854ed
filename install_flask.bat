@echo off
chcp 65001 >nul
echo ========================================
echo    Flask Installation Script
echo    سكريبت تثبيت Flask
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed
    echo.
    echo 📥 Please install Python first:
    echo 1. Microsoft Store: ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    echo 2. Python.org: https://python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found
python --version
echo.

REM Install Flask and Flask-SQLAlchemy
echo 📦 Installing Flask and Flask-SQLAlchemy...
echo.

echo Installing Flask...
python -m pip install Flask==2.3.3
if errorlevel 1 (
    echo ❌ Failed to install Flask
    goto :error
)
echo ✅ Flask installed

echo Installing Flask-SQLAlchemy...
python -m pip install Flask-SQLAlchemy==3.0.5
if errorlevel 1 (
    echo ❌ Failed to install Flask-SQLAlchemy
    goto :error
)
echo ✅ Flask-SQLAlchemy installed

echo Installing Werkzeug...
python -m pip install Werkzeug==2.3.7
if errorlevel 1 (
    echo ❌ Failed to install Werkzeug
    goto :error
)
echo ✅ Werkzeug installed

REM Test imports
echo.
echo 🧪 Testing imports...
python -c "import flask; print('✅ Flask import OK')"
if errorlevel 1 (
    echo ❌ Flask import failed
    goto :error
)

python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy import OK')"
if errorlevel 1 (
    echo ❌ Flask-SQLAlchemy import failed
    goto :error
)

python -c "from werkzeug.security import generate_password_hash; print('✅ Werkzeug import OK')"
if errorlevel 1 (
    echo ❌ Werkzeug import failed
    goto :error
)

echo.
echo 🎉 All packages installed successfully!
echo.
echo 🚀 You can now run the hotel management system:
echo    python app_simple.py
echo.
echo 🌐 Then open: http://localhost:5000
echo 👤 Login: admin / admin123
echo.
pause
exit /b 0

:error
echo.
echo ❌ Installation failed!
echo.
echo 🔧 Try these solutions:
echo 1. Run as Administrator
echo 2. Use: python -m pip install --user Flask Flask-SQLAlchemy
echo 3. Check internet connection
echo 4. Update pip: python -m pip install --upgrade pip
echo.
pause
exit /b 1
