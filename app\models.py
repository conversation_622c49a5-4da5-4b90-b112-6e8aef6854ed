from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), default='staff')  # 'admin', 'manager', 'staff'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Bo<PERSON>an, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class Room(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    room_number = db.Column(db.String(10), unique=True, nullable=False)
    room_type = db.Column(db.String(50), nullable=False)  # 'single', 'double', 'suite'
    price = db.Column(db.Float, nullable=False)
    is_available = db.Column(db.Boolean, default=True)
    description = db.Column(db.Text)
    max_occupancy = db.Column(db.Integer, default=1)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'room_number': self.room_number,
            'room_type': self.room_type,
            'price': self.price,
            'is_available': self.is_available,
            'description': self.description,
            'max_occupancy': self.max_occupancy
        }

    def __repr__(self):
        return f'<Room {self.room_number} - {self.room_type} - ${self.price}>'

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone = db.Column(db.String(15), nullable=False)
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address
        }

    def __repr__(self):
        return f'<Customer {self.name} - {self.email}>'

class Booking(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    room_id = db.Column(db.Integer, db.ForeignKey('room.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    check_in_date = db.Column(db.Date, nullable=False)
    check_out_date = db.Column(db.Date, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='confirmed')  # 'confirmed', 'checked_in', 'checked_out', 'cancelled'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)

    room = db.relationship('Room', backref='bookings')
    customer = db.relationship('Customer', backref='bookings')

    def calculate_total_price(self):
        """Calculate total price based on number of nights and room price"""
        nights = (self.check_out_date - self.check_in_date).days
        return nights * self.room.price

    def to_dict(self):
        return {
            'id': self.id,
            'room_id': self.room_id,
            'customer_id': self.customer_id,
            'check_in_date': self.check_in_date.isoformat(),
            'check_out_date': self.check_out_date.isoformat(),
            'total_price': self.total_price,
            'status': self.status,
            'notes': self.notes
        }

    def __repr__(self):
        return f'<Booking {self.id} - Room {self.room_id} - {self.status}>'

class Service(db.Model):
    """خدمات إضافية للفندق"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(50), nullable=False)  # 'food', 'spa', 'transport', 'entertainment'
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'price': self.price,
            'category': self.category,
            'is_active': self.is_active
        }

    def __repr__(self):
        return f'<Service {self.name} - {self.price}>'

class BookingService(db.Model):
    """خدمات مضافة للحجز"""
    id = db.Column(db.Integer, primary_key=True)
    booking_id = db.Column(db.Integer, db.ForeignKey('booking.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
    quantity = db.Column(db.Integer, default=1)
    total_price = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    booking = db.relationship('Booking', backref='booking_services')
    service = db.relationship('Service', backref='booking_services')

    def __repr__(self):
        return f'<BookingService {self.booking_id} - {self.service_id}>'

class Payment(db.Model):
    """مدفوعات الحجوزات"""
    id = db.Column(db.Integer, primary_key=True)
    booking_id = db.Column(db.Integer, db.ForeignKey('booking.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # 'cash', 'card', 'transfer'
    payment_status = db.Column(db.String(20), default='pending')  # 'pending', 'completed', 'failed'
    transaction_id = db.Column(db.String(100))
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)

    booking = db.relationship('Booking', backref='payments')

    def to_dict(self):
        return {
            'id': self.id,
            'booking_id': self.booking_id,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'payment_status': self.payment_status,
            'payment_date': self.payment_date.isoformat(),
            'notes': self.notes
        }

    def __repr__(self):
        return f'<Payment {self.id} - {self.amount} - {self.payment_status}>'

class Maintenance(db.Model):
    """صيانة الغرف"""
    id = db.Column(db.Integer, primary_key=True)
    room_id = db.Column(db.Integer, db.ForeignKey('room.id'), nullable=False)
    maintenance_type = db.Column(db.String(50), nullable=False)  # 'cleaning', 'repair', 'inspection'
    description = db.Column(db.Text, nullable=False)
    priority = db.Column(db.String(20), default='medium')  # 'low', 'medium', 'high', 'urgent'
    status = db.Column(db.String(20), default='pending')  # 'pending', 'in_progress', 'completed'
    assigned_to = db.Column(db.String(100))
    scheduled_date = db.Column(db.DateTime)
    completed_date = db.Column(db.DateTime)
    cost = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    room = db.relationship('Room', backref='maintenance_records')

    def to_dict(self):
        return {
            'id': self.id,
            'room_id': self.room_id,
            'maintenance_type': self.maintenance_type,
            'description': self.description,
            'priority': self.priority,
            'status': self.status,
            'assigned_to': self.assigned_to,
            'scheduled_date': self.scheduled_date.isoformat() if self.scheduled_date else None,
            'cost': self.cost
        }

    def __repr__(self):
        return f'<Maintenance {self.id} - Room {self.room_id} - {self.status}>'

class Review(db.Model):
    """تقييمات العملاء"""
    id = db.Column(db.Integer, primary_key=True)
    booking_id = db.Column(db.Integer, db.ForeignKey('booking.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    comment = db.Column(db.Text)
    room_rating = db.Column(db.Integer)  # 1-5 stars
    service_rating = db.Column(db.Integer)  # 1-5 stars
    cleanliness_rating = db.Column(db.Integer)  # 1-5 stars
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    booking = db.relationship('Booking', backref='reviews')
    customer = db.relationship('Customer', backref='reviews')

    def to_dict(self):
        return {
            'id': self.id,
            'booking_id': self.booking_id,
            'customer_name': self.customer.name,
            'rating': self.rating,
            'comment': self.comment,
            'room_rating': self.room_rating,
            'service_rating': self.service_rating,
            'cleanliness_rating': self.cleanliness_rating,
            'created_at': self.created_at.isoformat()
        }

    def __repr__(self):
        return f'<Review {self.id} - {self.rating} stars>'

class Notification(db.Model):
    """نظام الإشعارات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # 'booking', 'payment', 'maintenance', 'system'
    priority = db.Column(db.String(20), default='medium')  # 'low', 'medium', 'high'
    is_read = db.Column(db.Boolean, default=False)
    related_id = db.Column(db.Integer)  # ID of related object (booking, payment, etc.)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='notifications')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'notification_type': self.notification_type,
            'priority': self.priority,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat()
        }

    def __repr__(self):
        return f'<Notification {self.id} - {self.title}>'

class Report(db.Model):
    """تقارير النظام"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    report_type = db.Column(db.String(50), nullable=False)  # 'financial', 'occupancy', 'customer', 'maintenance'
    parameters = db.Column(db.Text)  # JSON string of report parameters
    generated_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    file_path = db.Column(db.String(500))
    status = db.Column(db.String(20), default='pending')  # 'pending', 'completed', 'failed'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='reports')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'report_type': self.report_type,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'generated_by': self.user.username
        }

    def __repr__(self):
        return f'<Report {self.id} - {self.name}>'

class RoomImage(db.Model):
    """صور الغرف"""
    id = db.Column(db.Integer, primary_key=True)
    room_id = db.Column(db.Integer, db.ForeignKey('room.id'), nullable=False)
    image_path = db.Column(db.String(500), nullable=False)
    caption = db.Column(db.String(200))
    is_primary = db.Column(db.Boolean, default=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    room = db.relationship('Room', backref='images')

    def to_dict(self):
        return {
            'id': self.id,
            'room_id': self.room_id,
            'image_path': self.image_path,
            'caption': self.caption,
            'is_primary': self.is_primary
        }

    def __repr__(self):
        return f'<RoomImage {self.id} - Room {self.room_id}>'

class HousekeepingTask(db.Model):
    """مهام التنظيف والخدمة"""
    id = db.Column(db.Integer, primary_key=True)
    room_id = db.Column(db.Integer, db.ForeignKey('room.id'), nullable=False)
    task_type = db.Column(db.String(50), nullable=False)  # 'cleaning', 'laundry', 'minibar', 'inspection'
    description = db.Column(db.Text)
    assigned_to = db.Column(db.String(100))
    priority = db.Column(db.String(20), default='medium')  # 'low', 'medium', 'high'
    status = db.Column(db.String(20), default='pending')  # 'pending', 'in_progress', 'completed'
    estimated_duration = db.Column(db.Integer)  # in minutes
    actual_duration = db.Column(db.Integer)  # in minutes
    scheduled_time = db.Column(db.DateTime)
    completed_time = db.Column(db.DateTime)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    room = db.relationship('Room', backref='housekeeping_tasks')

    def to_dict(self):
        return {
            'id': self.id,
            'room_id': self.room_id,
            'task_type': self.task_type,
            'description': self.description,
            'assigned_to': self.assigned_to,
            'priority': self.priority,
            'status': self.status,
            'scheduled_time': self.scheduled_time.isoformat() if self.scheduled_time else None,
            'estimated_duration': self.estimated_duration
        }

    def __repr__(self):
        return f'<HousekeepingTask {self.id} - Room {self.room_id} - {self.task_type}>'
