#!/usr/bin/env python3
"""
Simple Hotel Management System
نظام إدارة الفندق المبسط

This is a standalone version that works without complex dependencies
"""

import os
import sys
from datetime import datetime, date

# Try to import Flask components with better error handling
def check_and_install_flask():
    """Check if Flask is available and try to install if not"""
    try:
        from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
        from werkzeug.security import generate_password_hash, check_password_hash
        return True, None
    except ImportError as e:
        print(f"❌ Flask not available: {e}")
        print("🔧 Attempting to install Flask...")

        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "Flask"])
            print("✅ Flask installed successfully!")

            # Try importing again
            from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
            from werkzeug.security import generate_password_hash, check_password_hash
            return True, None
        except Exception as install_error:
            return False, f"Could not install Flask: {install_error}"

def check_and_install_sqlalchemy():
    """Check if Flask-SQLAlchemy is available and try to install if not"""
    try:
        from flask_sqlalchemy import SQLAlchemy
        return True, None
    except ImportError as e:
        print(f"❌ Flask-SQLAlchemy not available: {e}")
        print("🔧 Attempting to install Flask-SQLAlchemy...")

        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "Flask-SQLAlchemy"])
            print("✅ Flask-SQLAlchemy installed successfully!")

            # Try importing again
            from flask_sqlalchemy import SQLAlchemy
            return True, None
        except Exception as install_error:
            return False, f"Could not install Flask-SQLAlchemy: {install_error}"

# Check Flask
flask_ok, flask_error = check_and_install_flask()
if not flask_ok:
    print(f"❌ Flask error: {flask_error}")
    print("\n🔧 Manual installation required:")
    print("  1. Open Command Prompt as Administrator")
    print("  2. Run: pip install Flask")
    print("  3. Or run: python fix_imports.py")
    input("Press Enter to exit...")
    sys.exit(1)

# Check SQLAlchemy
sqlalchemy_ok, sqlalchemy_error = check_and_install_sqlalchemy()
if not sqlalchemy_ok:
    print(f"❌ SQLAlchemy error: {sqlalchemy_error}")
    print("\n🔧 Manual installation required:")
    print("  1. Open Command Prompt as Administrator")
    print("  2. Run: pip install Flask-SQLAlchemy")
    print("  3. Or run: python fix_imports.py")
    input("Press Enter to exit...")
    sys.exit(1)

# Now import everything
from flask import Flask, render_template, render_template_string, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'hotel_management_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hotel_simple.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db = SQLAlchemy(app)

# Simple Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), default='staff')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Room(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    room_number = db.Column(db.String(10), unique=True, nullable=False)
    room_type = db.Column(db.String(50), nullable=False)
    price = db.Column(db.Float, nullable=False)
    is_available = db.Column(db.Boolean, default=True)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone = db.Column(db.String(15), nullable=False)

class Booking(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    room_id = db.Column(db.Integer, db.ForeignKey('room.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    check_in_date = db.Column(db.Date, nullable=False)
    check_out_date = db.Column(db.Date, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='confirmed')

    room = db.relationship('Room', backref='bookings')
    customer = db.relationship('Customer', backref='bookings')

# Routes
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('فشل تسجيل الدخول. تحقق من اسم المستخدم وكلمة المرور.', 'danger')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح.', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    total_rooms = Room.query.count()
    available_rooms = Room.query.filter_by(is_available=True).count()
    booked_rooms = total_rooms - available_rooms
    recent_bookings = Booking.query.order_by(Booking.id.desc()).limit(5).all()

    return render_template_string(DASHBOARD_TEMPLATE,
                                total_rooms=total_rooms,
                                available_rooms=available_rooms,
                                booked_rooms=booked_rooms,
                                recent_bookings=recent_bookings,
                                username=session.get('username'))

@app.route('/rooms')
def rooms():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    rooms = Room.query.all()
    return render_template_string(ROOMS_TEMPLATE, rooms=rooms)

@app.route('/add_room', methods=['GET', 'POST'])
def add_room():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        room_number = request.form['room_number']
        room_type = request.form['room_type']
        price = float(request.form['price'])

        new_room = Room(room_number=room_number, room_type=room_type, price=price)
        db.session.add(new_room)
        db.session.commit()
        flash('تم إضافة الغرفة بنجاح!', 'success')
        return redirect(url_for('rooms'))

    return render_template_string(ADD_ROOM_TEMPLATE)

# HTML Templates as strings (for simplicity)
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #2c3e50, #3498db); min-height: 100vh; }
        .login-card { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container d-flex align-items-center justify-content-center min-vh-100">
        <div class="login-card" style="width: 100%; max-width: 400px;">
            <div class="text-center mb-4">
                <h2>🏨 نظام إدارة الفندق</h2>
                <p class="text-muted">تسجيل الدخول إلى حسابك</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
            </form>

            <div class="text-center mt-3">
                <small class="text-muted">المستخدم الافتراضي: admin / admin123</small>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">🏨 نظام إدارة الفندق</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                <a class="nav-link" href="{{ url_for('rooms') }}">الغرف</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>مرحباً، {{ username }}</h1>

        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-bed fa-2x text-primary mb-2"></i>
                        <h3>{{ total_rooms }}</h3>
                        <p>إجمالي الغرف</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h3>{{ available_rooms }}</h3>
                        <p>الغرف المتاحة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar-check fa-2x text-warning mb-2"></i>
                        <h3>{{ booked_rooms }}</h3>
                        <p>الغرف المحجوزة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-plus fa-2x text-info mb-2"></i>
                        <h3><a href="{{ url_for('add_room') }}" class="btn btn-primary">إضافة غرفة</a></h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

ROOMS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الغرف - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">🏨 نظام إدارة الفندق</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                <a class="nav-link" href="{{ url_for('rooms') }}">الغرف</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>إدارة الغرف</h1>
            <a href="{{ url_for('add_room') }}" class="btn btn-primary">إضافة غرفة جديدة</a>
        </div>

        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الغرفة</th>
                        <th>نوع الغرفة</th>
                        <th>السعر</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for room in rooms %}
                    <tr>
                        <td>{{ room.room_number }}</td>
                        <td>{{ room.room_type }}</td>
                        <td>{{ room.price }} ريال</td>
                        <td>
                            {% if room.is_available %}
                                <span class="badge bg-success">متاحة</span>
                            {% else %}
                                <span class="badge bg-danger">محجوزة</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
'''

ADD_ROOM_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة غرفة - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">🏨 نظام إدارة الفندق</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                <a class="nav-link" href="{{ url_for('rooms') }}">الغرف</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>إضافة غرفة جديدة</h1>

        <form method="POST" class="mt-4">
            <div class="mb-3">
                <label class="form-label">رقم الغرفة</label>
                <input type="text" class="form-control" name="room_number" required>
            </div>
            <div class="mb-3">
                <label class="form-label">نوع الغرفة</label>
                <select class="form-control" name="room_type" required>
                    <option value="single">مفردة</option>
                    <option value="double">مزدوجة</option>
                    <option value="suite">جناح</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">السعر (ريال)</label>
                <input type="number" class="form-control" name="price" step="0.01" required>
            </div>
            <button type="submit" class="btn btn-primary">إضافة الغرفة</button>
            <a href="{{ url_for('rooms') }}" class="btn btn-secondary">إلغاء</a>
        </form>
    </div>
</body>
</html>
'''

def create_default_user():
    """Create default admin user"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(username='admin', email='<EMAIL>', role='admin')
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("✅ Default admin user created: admin/admin123")

def create_sample_data():
    """Create sample rooms"""
    if Room.query.count() == 0:
        sample_rooms = [
            Room(room_number='101', room_type='single', price=150.0),
            Room(room_number='102', room_type='double', price=250.0),
            Room(room_number='201', room_type='suite', price=500.0),
        ]
        for room in sample_rooms:
            db.session.add(room)
        db.session.commit()
        print("✅ Sample rooms created")

if __name__ == '__main__':
    print("🏨 Starting Hotel Management System...")

    # Create database tables
    with app.app_context():
        db.create_all()
        create_default_user()
        create_sample_data()

    print("✅ Database initialized")
    print("🌐 Starting web server...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("👤 Login with: admin / admin123")

    app.run(debug=True, host='0.0.0.0', port=5000)
