#!/bin/bash

echo "========================================"
echo "   Hotel Management System"
echo "   نظام إدارة الفندق"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python is not installed"
        echo "Please install Python 3.7+ first"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python found: $($PYTHON_CMD --version)"
echo

# Try to install dependencies
echo "📦 Installing dependencies..."
$PYTHON_CMD -m pip install Flask Flask-SQLAlchemy --quiet
if [ $? -ne 0 ]; then
    echo "⚠️  Could not install dependencies automatically"
    echo "Trying simple version..."
    echo
    echo "🔄 Running simple version..."
    echo "🌐 Open your browser and go to: http://localhost:5000"
    echo "👤 Login: admin / admin123"
    echo
    $PYTHON_CMD app_simple.py
    exit 0
fi

echo "✅ Dependencies installed"
echo

# Try to run the full application
echo "🚀 Starting Hotel Management System..."
echo "🌐 Open your browser and go to: http://localhost:5000"
echo "👤 Login: admin / admin123"
echo "🛑 Press Ctrl+C to stop"
echo
$PYTHON_CMD run.py
