@echo off
chcp 65001 >nul
title Hotel Management System
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   🏨 Hotel Management System                 ║
echo ║                        نظام إدارة الفندق                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Quick check for Python and Flask
python -c "import flask, flask_sqlalchemy" >nul 2>&1
if errorlevel 1 (
    py -c "import flask, flask_sqlalchemy" >nul 2>&1
    if errorlevel 1 (
        echo ❌ Flask is not installed
        echo.
        echo 🔧 Quick Fix:
        echo    1. Double-click: INSTALL_NOW.bat
        echo    2. Or read: SETUP_GUIDE.md
        echo.
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo ✅ Flask is ready!
echo.
echo 🚀 Starting Hotel Management System...
echo 🌐 Open your browser: http://localhost:5000
echo 👤 Login: admin / admin123
echo 🛑 Press Ctrl+C to stop
echo.

if exist "app_simple.py" (
    %PYTHON_CMD% app_simple.py
) else if exist "run.py" (
    %PYTHON_CMD% run.py
) else (
    echo ❌ Application files not found
    pause
    exit /b 1
)

echo.
echo 👋 Application stopped.
pause
