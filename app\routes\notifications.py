from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from app.models import Notification, User, Booking, Payment, Maintenance, db
from app.routes.auth import login_required
from datetime import datetime, date, timedelta

notifications_bp = Blueprint('notifications', __name__)

@notifications_bp.route('/notifications')
@login_required
def view_notifications():
    """عرض الإشعارات"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    notifications = Notification.query.filter_by(
        user_id=session['user_id']
    ).order_by(
        Notification.is_read.asc(),
        Notification.created_at.desc()
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('notifications/list.html', notifications=notifications)

@notifications_bp.route('/notifications/mark_read/<int:notification_id>')
@login_required
def mark_notification_read(notification_id):
    """تمييز الإشعار كمقروء"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=session['user_id']
    ).first_or_404()
    
    notification.is_read = True
    db.session.commit()
    
    return redirect(url_for('notifications.view_notifications'))

@notifications_bp.route('/notifications/mark_all_read')
@login_required
def mark_all_notifications_read():
    """تمييز جميع الإشعارات كمقروءة"""
    Notification.query.filter_by(
        user_id=session['user_id'],
        is_read=False
    ).update({'is_read': True})
    
    db.session.commit()
    flash('تم تمييز جميع الإشعارات كمقروءة', 'success')
    return redirect(url_for('notifications.view_notifications'))

@notifications_bp.route('/notifications/delete/<int:notification_id>')
@login_required
def delete_notification(notification_id):
    """حذف إشعار"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=session['user_id']
    ).first_or_404()
    
    db.session.delete(notification)
    db.session.commit()
    flash('تم حذف الإشعار', 'success')
    return redirect(url_for('notifications.view_notifications'))

@notifications_bp.route('/api/notifications/unread_count')
@login_required
def api_unread_count():
    """API لعدد الإشعارات غير المقروءة"""
    count = Notification.query.filter_by(
        user_id=session['user_id'],
        is_read=False
    ).count()
    
    return jsonify({'count': count})

@notifications_bp.route('/api/notifications/recent')
@login_required
def api_recent_notifications():
    """API للإشعارات الحديثة"""
    notifications = Notification.query.filter_by(
        user_id=session['user_id']
    ).order_by(
        Notification.created_at.desc()
    ).limit(5).all()
    
    return jsonify([notification.to_dict() for notification in notifications])

def create_notification(user_id, title, message, notification_type, priority='medium', related_id=None):
    """إنشاء إشعار جديد"""
    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        notification_type=notification_type,
        priority=priority,
        related_id=related_id
    )
    
    db.session.add(notification)
    db.session.commit()
    return notification

def create_booking_notification(booking_id, notification_type):
    """إنشاء إشعار متعلق بالحجز"""
    booking = Booking.query.get(booking_id)
    if not booking:
        return
    
    # إشعار لجميع المديرين والموظفين
    users = User.query.filter(User.role.in_(['admin', 'manager', 'staff'])).all()
    
    if notification_type == 'new_booking':
        title = 'حجز جديد'
        message = f'تم إنشاء حجز جديد #{booking.id} للعميل {booking.customer.name}'
    elif notification_type == 'booking_cancelled':
        title = 'إلغاء حجز'
        message = f'تم إلغاء الحجز #{booking.id} للعميل {booking.customer.name}'
    elif notification_type == 'check_in_reminder':
        title = 'تذكير وصول'
        message = f'موعد وصول العميل {booking.customer.name} اليوم - الغرفة {booking.room.room_number}'
    elif notification_type == 'check_out_reminder':
        title = 'تذكير مغادرة'
        message = f'موعد مغادرة العميل {booking.customer.name} اليوم - الغرفة {booking.room.room_number}'
    else:
        return
    
    for user in users:
        create_notification(
            user_id=user.id,
            title=title,
            message=message,
            notification_type='booking',
            related_id=booking_id
        )

def create_payment_notification(payment_id):
    """إنشاء إشعار متعلق بالدفع"""
    payment = Payment.query.get(payment_id)
    if not payment:
        return
    
    # إشعار للمديرين المالي
    users = User.query.filter(User.role.in_(['admin', 'manager'])).all()
    
    if payment.payment_status == 'completed':
        title = 'دفعة مكتملة'
        message = f'تم استلام دفعة بمبلغ {payment.amount} ريال للحجز #{payment.booking_id}'
    elif payment.payment_status == 'failed':
        title = 'فشل في الدفع'
        message = f'فشل في معالجة دفعة بمبلغ {payment.amount} ريال للحجز #{payment.booking_id}'
    else:
        return
    
    for user in users:
        create_notification(
            user_id=user.id,
            title=title,
            message=message,
            notification_type='payment',
            related_id=payment_id
        )

def create_maintenance_notification(maintenance_id):
    """إنشاء إشعار متعلق بالصيانة"""
    maintenance = Maintenance.query.get(maintenance_id)
    if not maintenance:
        return
    
    # إشعار لفريق الصيانة والمديرين
    users = User.query.filter(User.role.in_(['admin', 'manager', 'staff'])).all()
    
    if maintenance.priority == 'urgent':
        title = 'صيانة عاجلة'
        message = f'طلب صيانة عاجل للغرفة {maintenance.room.room_number}: {maintenance.description}'
        priority = 'high'
    else:
        title = 'طلب صيانة جديد'
        message = f'طلب صيانة للغرفة {maintenance.room.room_number}: {maintenance.description}'
        priority = 'medium'
    
    for user in users:
        create_notification(
            user_id=user.id,
            title=title,
            message=message,
            notification_type='maintenance',
            priority=priority,
            related_id=maintenance_id
        )

def send_daily_reminders():
    """إرسال التذكيرات اليومية"""
    today = date.today()
    
    # تذكيرات الوصول
    check_ins_today = Booking.query.filter(
        Booking.check_in_date == today,
        Booking.status == 'confirmed'
    ).all()
    
    for booking in check_ins_today:
        create_booking_notification(booking.id, 'check_in_reminder')
    
    # تذكيرات المغادرة
    check_outs_today = Booking.query.filter(
        Booking.check_out_date == today,
        Booking.status == 'checked_in'
    ).all()
    
    for booking in check_outs_today:
        create_booking_notification(booking.id, 'check_out_reminder')
    
    # تذكيرات الصيانة المجدولة
    maintenance_today = Maintenance.query.filter(
        db.func.date(Maintenance.scheduled_date) == today,
        Maintenance.status == 'pending'
    ).all()
    
    for maintenance in maintenance_today:
        users = User.query.filter(User.role.in_(['admin', 'manager', 'staff'])).all()
        for user in users:
            create_notification(
                user_id=user.id,
                title='تذكير صيانة',
                message=f'صيانة مجدولة اليوم للغرفة {maintenance.room.room_number}',
                notification_type='maintenance',
                related_id=maintenance.id
            )

def send_weekly_summary():
    """إرسال ملخص أسبوعي"""
    week_ago = date.today() - timedelta(days=7)
    
    # إحصائيات الأسبوع
    weekly_bookings = Booking.query.filter(
        Booking.created_at >= week_ago
    ).count()
    
    weekly_revenue = db.session.query(func.sum(Payment.amount)).filter(
        Payment.payment_date >= week_ago,
        Payment.payment_status == 'completed'
    ).scalar() or 0
    
    # إرسال للمديرين
    managers = User.query.filter(User.role.in_(['admin', 'manager'])).all()
    
    for manager in managers:
        create_notification(
            user_id=manager.id,
            title='ملخص أسبوعي',
            message=f'الأسبوع الماضي: {weekly_bookings} حجز جديد، إيرادات {weekly_revenue} ريال',
            notification_type='system',
            priority='low'
        )
