from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash
from app.models import Room, db
from app.routes.auth import login_required

rooms_bp = Blueprint('rooms', __name__)

@rooms_bp.route('/rooms')
@login_required
def view_rooms():
    rooms = Room.query.all()
    return render_template('rooms.html', rooms=rooms)

@rooms_bp.route('/rooms/add', methods=['GET', 'POST'])
@login_required
def add_room():
    if request.method == 'POST':
        room_number = request.form['room_number']
        room_type = request.form['room_type']
        price = float(request.form['price'])
        description = request.form.get('description', '')
        max_occupancy = int(request.form.get('max_occupancy', 1))

        # Check if room number already exists
        if Room.query.filter_by(room_number=room_number).first():
            flash('رقم الغرفة موجود بالفعل!', 'danger')
            return render_template('add_room.html')

        new_room = Room(
            room_number=room_number,
            room_type=room_type,
            price=price,
            description=description,
            max_occupancy=max_occupancy
        )
        db.session.add(new_room)
        db.session.commit()
        flash('تم إضافة الغرفة بنجاح!', 'success')
        return redirect(url_for('rooms.view_rooms'))

    return render_template('add_room.html')

@rooms_bp.route('/rooms/edit/<int:room_id>', methods=['GET', 'POST'])
@login_required
def edit_room(room_id):
    room = Room.query.get_or_404(room_id)

    if request.method == 'POST':
        room.room_number = request.form['room_number']
        room.room_type = request.form['room_type']
        room.price = float(request.form['price'])
        room.description = request.form.get('description', '')
        room.max_occupancy = int(request.form.get('max_occupancy', 1))
        room.is_available = 'is_available' in request.form

        db.session.commit()
        flash('تم تحديث الغرفة بنجاح!', 'success')
        return redirect(url_for('rooms.view_rooms'))

    return render_template('edit_room.html', room=room)

@rooms_bp.route('/rooms/delete/<int:room_id>', methods=['POST'])
@login_required
def delete_room(room_id):
    room = Room.query.get_or_404(room_id)

    # Check if room has active bookings
    if room.bookings:
        flash('لا يمكن حذف الغرفة لأنها تحتوي على حجوزات!', 'danger')
        return redirect(url_for('rooms.view_rooms'))

    db.session.delete(room)
    db.session.commit()
    flash('تم حذف الغرفة بنجاح!', 'success')
    return redirect(url_for('rooms.view_rooms'))

@rooms_bp.route('/rooms/toggle/<int:room_id>', methods=['POST'])
@login_required
def toggle_availability(room_id):
    room = Room.query.get_or_404(room_id)
    room.is_available = not room.is_available
    db.session.commit()

    status = 'متاحة' if room.is_available else 'غير متاحة'
    flash(f'تم تغيير حالة الغرفة إلى {status}', 'success')
    return redirect(url_for('rooms.view_rooms'))