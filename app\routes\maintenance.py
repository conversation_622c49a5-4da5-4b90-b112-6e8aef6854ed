from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from app.models import Maintenance, Room, HousekeepingTask, db
from app.routes.auth import login_required
from datetime import datetime, date

maintenance_bp = Blueprint('maintenance', __name__)

@maintenance_bp.route('/maintenance')
@login_required
def view_maintenance():
    """عرض جميع طلبات الصيانة"""
    status_filter = request.args.get('status', 'all')
    priority_filter = request.args.get('priority', 'all')
    
    query = Maintenance.query
    
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)
    if priority_filter != 'all':
        query = query.filter_by(priority=priority_filter)
    
    maintenance_requests = query.order_by(Maintenance.created_at.desc()).all()
    
    return render_template('maintenance/list.html', 
                         maintenance_requests=maintenance_requests,
                         status_filter=status_filter,
                         priority_filter=priority_filter)

@maintenance_bp.route('/maintenance/add', methods=['GET', 'POST'])
@login_required
def add_maintenance():
    """إضافة طلب صيانة جديد"""
    if request.method == 'POST':
        room_id = request.form['room_id']
        maintenance_type = request.form['maintenance_type']
        description = request.form['description']
        priority = request.form['priority']
        assigned_to = request.form.get('assigned_to', '')
        scheduled_date = request.form.get('scheduled_date')
        
        new_maintenance = Maintenance(
            room_id=room_id,
            maintenance_type=maintenance_type,
            description=description,
            priority=priority,
            assigned_to=assigned_to
        )
        
        if scheduled_date:
            new_maintenance.scheduled_date = datetime.strptime(scheduled_date, '%Y-%m-%dT%H:%M')
        
        db.session.add(new_maintenance)
        db.session.commit()
        flash('تم إضافة طلب الصيانة بنجاح!', 'success')
        return redirect(url_for('maintenance.view_maintenance'))
    
    rooms = Room.query.all()
    return render_template('maintenance/add.html', rooms=rooms)

@maintenance_bp.route('/maintenance/edit/<int:maintenance_id>', methods=['GET', 'POST'])
@login_required
def edit_maintenance(maintenance_id):
    """تعديل طلب صيانة"""
    maintenance = Maintenance.query.get_or_404(maintenance_id)
    
    if request.method == 'POST':
        maintenance.maintenance_type = request.form['maintenance_type']
        maintenance.description = request.form['description']
        maintenance.priority = request.form['priority']
        maintenance.status = request.form['status']
        maintenance.assigned_to = request.form.get('assigned_to', '')
        maintenance.cost = float(request.form.get('cost', 0))
        
        scheduled_date = request.form.get('scheduled_date')
        if scheduled_date:
            maintenance.scheduled_date = datetime.strptime(scheduled_date, '%Y-%m-%dT%H:%M')
        
        if maintenance.status == 'completed' and not maintenance.completed_date:
            maintenance.completed_date = datetime.utcnow()
        
        db.session.commit()
        flash('تم تحديث طلب الصيانة بنجاح!', 'success')
        return redirect(url_for('maintenance.view_maintenance'))
    
    return render_template('maintenance/edit.html', maintenance=maintenance)

@maintenance_bp.route('/maintenance/delete/<int:maintenance_id>', methods=['POST'])
@login_required
def delete_maintenance(maintenance_id):
    """حذف طلب صيانة"""
    maintenance = Maintenance.query.get_or_404(maintenance_id)
    db.session.delete(maintenance)
    db.session.commit()
    flash('تم حذف طلب الصيانة بنجاح!', 'success')
    return redirect(url_for('maintenance.view_maintenance'))

@maintenance_bp.route('/housekeeping')
@login_required
def view_housekeeping():
    """عرض مهام التنظيف"""
    status_filter = request.args.get('status', 'all')
    date_filter = request.args.get('date', '')
    
    query = HousekeepingTask.query
    
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)
    if date_filter:
        filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
        query = query.filter(db.func.date(HousekeepingTask.scheduled_time) == filter_date)
    
    tasks = query.order_by(HousekeepingTask.scheduled_time.asc()).all()
    
    return render_template('maintenance/housekeeping.html', 
                         tasks=tasks,
                         status_filter=status_filter,
                         date_filter=date_filter)

@housekeeping_bp.route('/housekeeping/add', methods=['GET', 'POST'])
@login_required
def add_housekeeping_task():
    """إضافة مهمة تنظيف جديدة"""
    if request.method == 'POST':
        room_id = request.form['room_id']
        task_type = request.form['task_type']
        description = request.form.get('description', '')
        assigned_to = request.form.get('assigned_to', '')
        priority = request.form['priority']
        estimated_duration = int(request.form.get('estimated_duration', 30))
        scheduled_time = request.form.get('scheduled_time')
        
        new_task = HousekeepingTask(
            room_id=room_id,
            task_type=task_type,
            description=description,
            assigned_to=assigned_to,
            priority=priority,
            estimated_duration=estimated_duration
        )
        
        if scheduled_time:
            new_task.scheduled_time = datetime.strptime(scheduled_time, '%Y-%m-%dT%H:%M')
        
        db.session.add(new_task)
        db.session.commit()
        flash('تم إضافة مهمة التنظيف بنجاح!', 'success')
        return redirect(url_for('maintenance.view_housekeeping'))
    
    rooms = Room.query.all()
    return render_template('maintenance/add_housekeeping.html', rooms=rooms)

@maintenance_bp.route('/housekeeping/update_status/<int:task_id>', methods=['POST'])
@login_required
def update_housekeeping_status(task_id):
    """تحديث حالة مهمة التنظيف"""
    task = HousekeepingTask.query.get_or_404(task_id)
    new_status = request.form['status']
    
    task.status = new_status
    if new_status == 'completed':
        task.completed_time = datetime.utcnow()
        actual_duration = request.form.get('actual_duration')
        if actual_duration:
            task.actual_duration = int(actual_duration)
    
    task.notes = request.form.get('notes', '')
    
    db.session.commit()
    flash('تم تحديث حالة المهمة بنجاح!', 'success')
    return redirect(url_for('maintenance.view_housekeeping'))

@maintenance_bp.route('/api/maintenance/room/<int:room_id>')
@login_required
def api_room_maintenance(room_id):
    """API للحصول على تاريخ صيانة الغرفة"""
    maintenance_records = Maintenance.query.filter_by(room_id=room_id).order_by(Maintenance.created_at.desc()).all()
    return jsonify([record.to_dict() for record in maintenance_records])

@maintenance_bp.route('/api/housekeeping/today')
@login_required
def api_today_housekeeping():
    """API لمهام التنظيف اليوم"""
    today = date.today()
    tasks = HousekeepingTask.query.filter(
        db.func.date(HousekeepingTask.scheduled_time) == today
    ).all()
    return jsonify([task.to_dict() for task in tasks])

@maintenance_bp.route('/maintenance/calendar')
@login_required
def maintenance_calendar():
    """تقويم الصيانة والتنظيف"""
    return render_template('maintenance/calendar.html')
