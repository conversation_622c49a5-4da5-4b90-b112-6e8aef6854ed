{"python.defaultInterpreterPath": "python", "python.analysis.extraPaths": ["./", "./app", "./venv/Lib/site-packages", "./hotel_env/Lib/site-packages"], "python.analysis.autoImportCompletions": true, "python.analysis.diagnosticMode": "workspace", "python.analysis.typeCheckingMode": "basic", "python.analysis.autoSearchPaths": true, "python.analysis.stubPath": "./typings", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.analysis.diagnosticSeverityOverrides": {"reportMissingImports": "information", "reportMissingModuleSource": "information"}, "files.associations": {"*.py": "python"}, "python.terminal.activateEnvironment": true, "python.envFile": "${workspaceFolder}/.env"}