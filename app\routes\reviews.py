from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from app.models import Review, Booking, Customer, Room, db
from app.routes.auth import login_required
from datetime import datetime, date
from sqlalchemy import func

reviews_bp = Blueprint('reviews', __name__)

@reviews_bp.route('/reviews')
@login_required
def view_reviews():
    """عرض جميع التقييمات"""
    page = request.args.get('page', 1, type=int)
    rating_filter = request.args.get('rating', 'all')
    per_page = 20
    
    query = Review.query
    
    if rating_filter != 'all':
        query = query.filter_by(rating=int(rating_filter))
    
    reviews = query.order_by(Review.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات التقييمات
    avg_rating = db.session.query(func.avg(Review.rating)).scalar() or 0
    total_reviews = Review.query.count()
    
    # توزيع التقييمات
    rating_distribution = {}
    for i in range(1, 6):
        count = Review.query.filter_by(rating=i).count()
        rating_distribution[i] = count
    
    return render_template('reviews/list.html', 
                         reviews=reviews,
                         avg_rating=round(avg_rating, 2),
                         total_reviews=total_reviews,
                         rating_distribution=rating_distribution,
                         rating_filter=rating_filter)

@reviews_bp.route('/reviews/add/<int:booking_id>', methods=['GET', 'POST'])
@login_required
def add_review(booking_id):
    """إضافة تقييم للحجز"""
    booking = Booking.query.get_or_404(booking_id)
    
    # التحقق من أن الحجز مكتمل
    if booking.status != 'checked_out':
        flash('لا يمكن إضافة تقييم إلا بعد انتهاء الإقامة', 'warning')
        return redirect(url_for('bookings.manage_bookings'))
    
    # التحقق من عدم وجود تقييم سابق
    existing_review = Review.query.filter_by(booking_id=booking_id).first()
    if existing_review:
        flash('تم إضافة تقييم لهذا الحجز مسبقاً', 'warning')
        return redirect(url_for('reviews.view_reviews'))
    
    if request.method == 'POST':
        rating = int(request.form['rating'])
        comment = request.form.get('comment', '')
        room_rating = int(request.form.get('room_rating', rating))
        service_rating = int(request.form.get('service_rating', rating))
        cleanliness_rating = int(request.form.get('cleanliness_rating', rating))
        
        new_review = Review(
            booking_id=booking_id,
            customer_id=booking.customer_id,
            rating=rating,
            comment=comment,
            room_rating=room_rating,
            service_rating=service_rating,
            cleanliness_rating=cleanliness_rating
        )
        
        db.session.add(new_review)
        db.session.commit()
        flash('تم إضافة التقييم بنجاح!', 'success')
        return redirect(url_for('reviews.view_reviews'))
    
    return render_template('reviews/add.html', booking=booking)

@reviews_bp.route('/reviews/edit/<int:review_id>', methods=['GET', 'POST'])
@login_required
def edit_review(review_id):
    """تعديل تقييم"""
    review = Review.query.get_or_404(review_id)
    
    if request.method == 'POST':
        review.rating = int(request.form['rating'])
        review.comment = request.form.get('comment', '')
        review.room_rating = int(request.form.get('room_rating', review.rating))
        review.service_rating = int(request.form.get('service_rating', review.rating))
        review.cleanliness_rating = int(request.form.get('cleanliness_rating', review.rating))
        
        db.session.commit()
        flash('تم تحديث التقييم بنجاح!', 'success')
        return redirect(url_for('reviews.view_reviews'))
    
    return render_template('reviews/edit.html', review=review)

@reviews_bp.route('/reviews/delete/<int:review_id>', methods=['POST'])
@login_required
def delete_review(review_id):
    """حذف تقييم"""
    review = Review.query.get_or_404(review_id)
    db.session.delete(review)
    db.session.commit()
    flash('تم حذف التقييم بنجاح!', 'success')
    return redirect(url_for('reviews.view_reviews'))

@reviews_bp.route('/reviews/room/<int:room_id>')
@login_required
def room_reviews(room_id):
    """عرض تقييمات غرفة محددة"""
    room = Room.query.get_or_404(room_id)
    
    reviews = db.session.query(Review).join(Booking).filter(
        Booking.room_id == room_id
    ).order_by(Review.created_at.desc()).all()
    
    # إحصائيات الغرفة
    avg_rating = db.session.query(func.avg(Review.rating)).join(Booking).filter(
        Booking.room_id == room_id
    ).scalar() or 0
    
    avg_room_rating = db.session.query(func.avg(Review.room_rating)).join(Booking).filter(
        Booking.room_id == room_id
    ).scalar() or 0
    
    avg_cleanliness = db.session.query(func.avg(Review.cleanliness_rating)).join(Booking).filter(
        Booking.room_id == room_id
    ).scalar() or 0
    
    return render_template('reviews/room_reviews.html',
                         room=room,
                         reviews=reviews,
                         avg_rating=round(avg_rating, 2),
                         avg_room_rating=round(avg_room_rating, 2),
                         avg_cleanliness=round(avg_cleanliness, 2))

@reviews_bp.route('/reviews/customer/<int:customer_id>')
@login_required
def customer_reviews(customer_id):
    """عرض تقييمات عميل محدد"""
    customer = Customer.query.get_or_404(customer_id)
    reviews = Review.query.filter_by(customer_id=customer_id).order_by(Review.created_at.desc()).all()
    
    return render_template('reviews/customer_reviews.html',
                         customer=customer,
                         reviews=reviews)

@reviews_bp.route('/reviews/analytics')
@login_required
def reviews_analytics():
    """تحليلات التقييمات"""
    # متوسط التقييمات الشامل
    overall_stats = {
        'avg_rating': db.session.query(func.avg(Review.rating)).scalar() or 0,
        'avg_room_rating': db.session.query(func.avg(Review.room_rating)).scalar() or 0,
        'avg_service_rating': db.session.query(func.avg(Review.service_rating)).scalar() or 0,
        'avg_cleanliness_rating': db.session.query(func.avg(Review.cleanliness_rating)).scalar() or 0,
        'total_reviews': Review.query.count()
    }
    
    # التقييمات حسب نوع الغرفة
    room_type_ratings = db.session.query(
        Room.room_type,
        func.avg(Review.rating).label('avg_rating'),
        func.count(Review.id).label('review_count')
    ).join(Booking).join(Review).group_by(Room.room_type).all()
    
    # التقييمات الشهرية
    monthly_ratings = db.session.query(
        func.extract('year', Review.created_at).label('year'),
        func.extract('month', Review.created_at).label('month'),
        func.avg(Review.rating).label('avg_rating'),
        func.count(Review.id).label('review_count')
    ).group_by(
        func.extract('year', Review.created_at),
        func.extract('month', Review.created_at)
    ).order_by('year', 'month').all()
    
    # أفضل وأسوأ التقييمات
    best_reviews = Review.query.filter(Review.rating >= 4).order_by(Review.created_at.desc()).limit(5).all()
    worst_reviews = Review.query.filter(Review.rating <= 2).order_by(Review.created_at.desc()).limit(5).all()
    
    return render_template('reviews/analytics.html',
                         overall_stats=overall_stats,
                         room_type_ratings=room_type_ratings,
                         monthly_ratings=monthly_ratings,
                         best_reviews=best_reviews,
                         worst_reviews=worst_reviews)

@reviews_bp.route('/api/reviews/stats')
@login_required
def api_reviews_stats():
    """API لإحصائيات التقييمات"""
    stats = {
        'avg_rating': db.session.query(func.avg(Review.rating)).scalar() or 0,
        'total_reviews': Review.query.count(),
        'rating_distribution': {}
    }
    
    # توزيع التقييمات
    for i in range(1, 6):
        count = Review.query.filter_by(rating=i).count()
        stats['rating_distribution'][i] = count
    
    return jsonify(stats)

@reviews_bp.route('/api/reviews/recent')
@login_required
def api_recent_reviews():
    """API للتقييمات الحديثة"""
    reviews = Review.query.order_by(Review.created_at.desc()).limit(10).all()
    return jsonify([review.to_dict() for review in reviews])

@reviews_bp.route('/reviews/respond/<int:review_id>', methods=['POST'])
@login_required
def respond_to_review(review_id):
    """الرد على تقييم"""
    review = Review.query.get_or_404(review_id)
    response = request.form.get('response', '')
    
    # يمكن إضافة نموذج للردود أو حفظ الرد في حقل منفصل
    # هنا سنضيف الرد إلى ملاحظات التقييم
    if review.comment:
        review.comment += f"\n\nرد الإدارة: {response}"
    else:
        review.comment = f"رد الإدارة: {response}"
    
    db.session.commit()
    flash('تم إضافة الرد بنجاح!', 'success')
    return redirect(url_for('reviews.view_reviews'))

@reviews_bp.route('/reviews/export')
@login_required
def export_reviews():
    """تصدير التقييمات"""
    # يمكن إضافة تصدير CSV أو Excel
    reviews = Review.query.all()
    
    # إنشاء ملف CSV
    import csv
    import io
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة العناوين
    writer.writerow(['رقم التقييم', 'العميل', 'الغرفة', 'التقييم العام', 'تقييم الغرفة', 
                     'تقييم الخدمة', 'تقييم النظافة', 'التعليق', 'التاريخ'])
    
    # كتابة البيانات
    for review in reviews:
        writer.writerow([
            review.id,
            review.customer.name,
            review.booking.room.room_number,
            review.rating,
            review.room_rating,
            review.service_rating,
            review.cleanliness_rating,
            review.comment,
            review.created_at.strftime('%Y-%m-%d')
        ])
    
    output.seek(0)
    
    from flask import Response
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment; filename=reviews.csv'}
    )
