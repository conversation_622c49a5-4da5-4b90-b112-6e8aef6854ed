# 🚀 بداية سريعة - نظام إدارة الفندق

## ❌ المشكلة الحالية
```
Import "flask_sqlalchemy" could not be resolved
Import "werkzeug.security" could not be resolved
```

## ✅ الحل في 3 خطوات

### الخطوة 1: تثبيت Python
انقر نقراً مزدوجاً على: **`INSTALL_NOW.bat`**

### الخطوة 2: تشغيل النظام  
انقر نقراً مزدوجاً على: **`RUN_HOTEL.bat`**

### الخطوة 3: فتح المتصفح
اذهب إلى: **http://localhost:5000**

---

## 🔧 إذا لم تعمل الطريقة السابقة

### الحل اليدوي:

#### 1. تثبيت Python:
- **Microsoft Store**: ابحث عن "Python 3.11"
- **python.org**: حمل من https://python.org/downloads/
- **مهم**: ✅ تأكد من تحديد "Add Python to PATH"

#### 2. تثبيت Flask:
```bash
python -m pip install Flask Flask-SQLAlchemy
```

#### 3. تشغيل النظام:
```bash
python app_simple.py
```

---

## 📱 بيانات تسجيل الدخول
- **اسم المستخدم**: admin  
- **كلمة المرور**: admin123

---

## 🆘 إذا استمرت المشاكل

### مشكلة: "python is not recognized"
**الحل**: أعد تثبيت Python مع تحديد "Add to PATH"

### مشكلة: "pip is not recognized"  
**الحل**: استخدم `python -m pip` بدلاً من `pip`

### مشكلة: "Permission denied"
**الحل**: شغل Command Prompt كمدير

### مشكلة: VS Code يظهر أخطاء استيراد
**الحل**: هذه تحذيرات فقط، النظام سيعمل بعد تثبيت Flask

---

## 📋 ملفات التشغيل المتوفرة

| الملف | الوصف |
|-------|--------|
| `INSTALL_NOW.bat` | تثبيت تلقائي لـ Python و Flask |
| `RUN_HOTEL.bat` | تشغيل سريع للنظام |
| `start.bat` | تشغيل ذكي مع فحص المتطلبات |
| `install_flask.bat` | تثبيت Flask فقط |

---

## 🎯 ملخص سريع

```bash
# الطريقة الأسرع:
INSTALL_NOW.bat  # ثم
RUN_HOTEL.bat

# أو يدوياً:
python -m pip install Flask Flask-SQLAlchemy
python app_simple.py

# ثم افتح: http://localhost:5000
# تسجيل دخول: admin / admin123
```

---

## 🏨 ميزات النظام

✅ إدارة الغرف والحجوزات  
✅ قاعدة بيانات العملاء  
✅ لوحة تحكم تفاعلية  
✅ تقارير وإحصائيات  
✅ واجهة عربية جميلة  

**🎉 استمتع بنظام إدارة الفندق!**
