<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    {% include 'base.html' %}
    <div class="container">
        <h1>Customer Management</h1>
        <a href="{{ url_for('customers.add') }}" class="btn btn-primary">Add New Customer</a>
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for customer in customers %}
                <tr>
                    <td>{{ customer.id }}</td>
                    <td>{{ customer.name }}</td>
                    <td>{{ customer.email }}</td>
                    <td>{{ customer.phone }}</td>
                    <td>
                        <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" class="btn btn-warning">Edit</a>
                        <a href="{{ url_for('customers.delete', customer_id=customer.id) }}" class="btn btn-danger">Delete</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>