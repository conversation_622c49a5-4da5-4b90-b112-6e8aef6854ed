#!/usr/bin/env python3
"""
Standalone Models for Hotel Management System
نماذج مستقلة لنظام إدارة الفندق

This file works without Flask-SQLAlchemy for demonstration purposes
"""

import sqlite3
import hashlib
import json
from datetime import datetime, date
from typing import List, Dict, Optional

class DatabaseManager:
    """Simple database manager using SQLite"""
    
    def __init__(self, db_path: str = "hotel_simple.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        return conn
    
    def init_database(self):
        """Initialize database with tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT DEFAULT 'staff',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Rooms table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rooms (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                room_number TEXT UNIQUE NOT NULL,
                room_type TEXT NOT NULL,
                price REAL NOT NULL,
                is_available BOOLEAN DEFAULT 1,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Customers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                phone TEXT NOT NULL,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Bookings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bookings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                room_id INTEGER NOT NULL,
                customer_id INTEGER NOT NULL,
                check_in_date DATE NOT NULL,
                check_out_date DATE NOT NULL,
                total_price REAL NOT NULL,
                status TEXT DEFAULT 'confirmed',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (room_id) REFERENCES rooms (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user
        self.create_default_admin()
        
        # Create sample data
        self.create_sample_data()
    
    def create_default_admin(self):
        """Create default admin user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if admin exists
        cursor.execute("SELECT id FROM users WHERE username = ?", ("admin",))
        if cursor.fetchone():
            conn.close()
            return
        
        # Create admin user
        password_hash = self.hash_password("admin123")
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        ''', ("admin", "<EMAIL>", password_hash, "admin"))
        
        conn.commit()
        conn.close()
        print("✅ Default admin user created: admin/admin123")
    
    def create_sample_data(self):
        """Create sample rooms and customers"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if sample data exists
        cursor.execute("SELECT COUNT(*) FROM rooms")
        room_count = cursor.fetchone()[0]
        
        if room_count == 0:
            # Sample rooms
            sample_rooms = [
                ("101", "single", 150.0, "غرفة مفردة مريحة"),
                ("102", "double", 250.0, "غرفة مزدوجة واسعة"),
                ("201", "suite", 500.0, "جناح فاخر مع إطلالة"),
                ("202", "single", 150.0, "غرفة مفردة هادئة"),
                ("301", "double", 280.0, "غرفة مزدوجة مع شرفة"),
            ]
            
            for room_number, room_type, price, description in sample_rooms:
                cursor.execute('''
                    INSERT INTO rooms (room_number, room_type, price, description)
                    VALUES (?, ?, ?, ?)
                ''', (room_number, room_type, price, description))
            
            print("✅ Sample rooms created")
        
        # Check if sample customers exist
        cursor.execute("SELECT COUNT(*) FROM customers")
        customer_count = cursor.fetchone()[0]
        
        if customer_count == 0:
            # Sample customers
            sample_customers = [
                ("أحمد محمد", "<EMAIL>", "0501234567", "الرياض"),
                ("فاطمة علي", "<EMAIL>", "0507654321", "جدة"),
                ("محمد سالم", "<EMAIL>", "0509876543", "الدمام"),
            ]
            
            for name, email, phone, address in sample_customers:
                cursor.execute('''
                    INSERT INTO customers (name, email, phone, address)
                    VALUES (?, ?, ?, ?)
                ''', (name, email, phone, address))
            
            print("✅ Sample customers created")
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    @staticmethod
    def check_password(password: str, password_hash: str) -> bool:
        """Check if password matches hash"""
        return hashlib.sha256(password.encode()).hexdigest() == password_hash

class User:
    """User model"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def authenticate(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, role, password_hash
            FROM users WHERE username = ?
        ''', (username,))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and self.db.check_password(password, user['password_hash']):
            return dict(user)
        return None
    
    def get_all(self) -> List[Dict]:
        """Get all users"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, username, email, role, created_at FROM users")
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return users

class Room:
    """Room model"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_all(self) -> List[Dict]:
        """Get all rooms"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM rooms ORDER BY room_number")
        rooms = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return rooms
    
    def get_available(self) -> List[Dict]:
        """Get available rooms"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM rooms WHERE is_available = 1 ORDER BY room_number")
        rooms = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return rooms
    
    def add(self, room_number: str, room_type: str, price: float, description: str = "") -> bool:
        """Add new room"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO rooms (room_number, room_type, price, description)
                VALUES (?, ?, ?, ?)
            ''', (room_number, room_type, price, description))
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
    
    def get_stats(self) -> Dict:
        """Get room statistics"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM rooms")
        total_rooms = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM rooms WHERE is_available = 1")
        available_rooms = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_rooms': total_rooms,
            'available_rooms': available_rooms,
            'booked_rooms': total_rooms - available_rooms
        }

class Customer:
    """Customer model"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_all(self) -> List[Dict]:
        """Get all customers"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM customers ORDER BY name")
        customers = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return customers
    
    def add(self, name: str, email: str, phone: str, address: str = "") -> bool:
        """Add new customer"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO customers (name, email, phone, address)
                VALUES (?, ?, ?, ?)
            ''', (name, email, phone, address))
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False

class Booking:
    """Booking model"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_all(self) -> List[Dict]:
        """Get all bookings with room and customer details"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT b.*, r.room_number, r.room_type, c.name as customer_name, c.email
            FROM bookings b
            JOIN rooms r ON b.room_id = r.id
            JOIN customers c ON b.customer_id = c.id
            ORDER BY b.created_at DESC
        ''')
        bookings = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return bookings
    
    def add(self, room_id: int, customer_id: int, check_in: str, check_out: str, total_price: float) -> bool:
        """Add new booking"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO bookings (room_id, customer_id, check_in_date, check_out_date, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (room_id, customer_id, check_in, check_out, total_price))
            
            # Update room availability
            cursor.execute("UPDATE rooms SET is_available = 0 WHERE id = ?", (room_id,))
            
            conn.commit()
            conn.close()
            return True
        except Exception:
            return False

# Example usage
if __name__ == "__main__":
    print("🏨 Hotel Management System - Standalone Models Test")
    print("=" * 50)
    
    # Initialize database
    db = DatabaseManager()
    
    # Test models
    user_model = User(db)
    room_model = Room(db)
    customer_model = Customer(db)
    booking_model = Booking(db)
    
    # Test authentication
    user = user_model.authenticate("admin", "admin123")
    if user:
        print(f"✅ Authentication successful: {user['username']}")
    else:
        print("❌ Authentication failed")
    
    # Test room stats
    stats = room_model.get_stats()
    print(f"📊 Room Stats: {stats}")
    
    # Test data retrieval
    rooms = room_model.get_all()
    print(f"🏠 Total rooms: {len(rooms)}")
    
    customers = customer_model.get_all()
    print(f"👥 Total customers: {len(customers)}")
    
    bookings = booking_model.get_all()
    print(f"📅 Total bookings: {len(bookings)}")
    
    print("\n✅ All tests passed! Database is working correctly.")
