@echo off
chcp 65001 >nul
echo ========================================
echo    Hotel Management System
echo    نظام إدارة الفندق
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    REM Try py command (Windows Python Launcher)
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python is not installed
        echo.
        echo 📥 Quick Installation Guide:
        echo.
        echo 🏪 Option 1: Microsoft Store ^(Easiest^)
        echo    1. Press Win+S and search "Microsoft Store"
        echo    2. Search for "Python 3.11"
        echo    3. Click "Get" or "Install"
        echo.
        echo 🌐 Option 2: python.org
        echo    1. Go to: https://python.org/downloads/
        echo    2. Download Python 3.11+
        echo    3. ✅ Check "Add Python to PATH" during installation
        echo.
        echo 📋 Option 3: Run installation script
        echo    Double-click: install_flask.bat
        echo.
        set /p choice="Press 1 for Microsoft Store, 2 for python.org, or Enter to exit: "

        if "%choice%"=="1" (
            echo 🏪 Opening Microsoft Store...
            start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
        )

        if "%choice%"=="2" (
            echo 🌐 Opening python.org...
            start https://www.python.org/downloads/
        )

        echo.
        echo After installing Python, run this script again.
        echo Or read SETUP_GUIDE.md for detailed instructions.
        pause
        exit /b 1
    ) else (
        echo ✅ Python found ^(using py command^)
        set PYTHON_CMD=py
    )
) else (
    echo ✅ Python found
    set PYTHON_CMD=python
)

REM Try to install Flask
echo 📦 Installing Flask and dependencies...
%PYTHON_CMD% -m pip install Flask Flask-SQLAlchemy --quiet >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Could not install Flask with pip, trying alternative...
    %PYTHON_CMD% -m pip install --user Flask Flask-SQLAlchemy --quiet >nul 2>&1
    if errorlevel 1 (
        echo ❌ Could not install Flask automatically
        echo.
        echo 🔧 Manual installation required:
        echo    %PYTHON_CMD% -m pip install Flask Flask-SQLAlchemy
        echo.
        echo 📖 Or read SETUP_GUIDE.md for detailed instructions
        echo.
        set /p continue="Press Enter to try simple version anyway..."
        goto :simple
    )
)

echo ✅ Flask packages installed
echo.

REM Test Flask imports
echo 🧪 Testing Flask imports...
%PYTHON_CMD% -c "import flask; print('✅ Flask import OK')" 2>nul
if errorlevel 1 (
    echo ⚠️ Flask import failed, using simple version
    goto :simple
)

%PYTHON_CMD% -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy import OK')" 2>nul
if errorlevel 1 (
    echo ⚠️ Flask-SQLAlchemy import failed, using simple version
    goto :simple
)

echo ✅ All imports successful
echo.

REM Try to run the application
echo 🚀 Starting Hotel Management System...
echo 🌐 Open your browser: http://localhost:5000
echo 👤 Login: admin / admin123
echo 🛑 Press Ctrl+C to stop
echo.

if exist "app_simple.py" (
    echo Running app_simple.py...
    %PYTHON_CMD% app_simple.py
) else if exist "run.py" (
    echo Running run.py...
    %PYTHON_CMD% run.py
) else (
    echo ❌ No application files found
    echo Please make sure you're in the correct directory
    pause
    exit /b 1
)

goto :end

:simple
echo.
echo 🔄 Running simple version without Flask...
echo.
echo ⚠️ Flask is not available, but you can still see the demo
echo.
echo 📖 To install Flask properly:
echo    1. Read SETUP_GUIDE.md
echo    2. Or run: install_flask.bat
echo    3. Or manually: %PYTHON_CMD% -m pip install Flask Flask-SQLAlchemy
echo.

if exist "models_standalone.py" (
    echo 🧪 Testing standalone models...
    %PYTHON_CMD% models_standalone.py
    echo.
    echo ✅ Database models are working!
    echo 📝 Check SETUP_GUIDE.md to install Flask for the full web interface
) else if exist "app_simple.py" (
    echo 🔄 Trying app_simple.py anyway...
    %PYTHON_CMD% app_simple.py
) else (
    echo ❌ No fallback files found
    echo Please check if all files are present
)

echo.
pause
goto :end

:end
echo.
echo 👋 Application stopped.
echo 📖 For help, read SETUP_GUIDE.md
pause
