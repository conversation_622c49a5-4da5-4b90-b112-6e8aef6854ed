from flask import Blueprint, render_template, session, redirect, url_for, flash
from app.models import Room, Booking, Customer, db
from app.routes.auth import login_required
from datetime import datetime, date
from sqlalchemy import func

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_rooms = Room.query.count()

    # Count current bookings (checked in or confirmed)
    current_bookings = Booking.query.filter(
        Booking.status.in_(['confirmed', 'checked_in'])
    ).count()

    available_rooms = total_rooms - current_bookings

    # Calculate income
    daily_income = calculate_daily_income()
    monthly_income = calculate_monthly_income()

    # Recent bookings
    recent_bookings = Booking.query.order_by(Booking.created_at.desc()).limit(5).all()

    # Room occupancy by type
    room_stats = db.session.query(
        Room.room_type,
        func.count(Room.id).label('total'),
        func.sum(func.case([(Room.is_available == True, 1)], else_=0)).label('available')
    ).group_by(Room.room_type).all()

    return render_template('dashboard.html',
                           total_rooms=total_rooms,
                           booked_rooms=current_bookings,
                           available_rooms=available_rooms,
                           daily_income=daily_income,
                           monthly_income=monthly_income,
                           recent_bookings=recent_bookings,
                           room_stats=room_stats,
                           username=session.get('username'))

def calculate_daily_income():
    """Calculate income for today"""
    today = date.today()
    daily_bookings = Booking.query.filter(
        Booking.check_in_date <= today,
        Booking.check_out_date > today,
        Booking.status.in_(['confirmed', 'checked_in'])
    ).all()

    total = sum(booking.total_price / (booking.check_out_date - booking.check_in_date).days
                for booking in daily_bookings)
    return round(total, 2)

def calculate_monthly_income():
    """Calculate income for current month"""
    today = date.today()
    first_day = today.replace(day=1)

    monthly_bookings = Booking.query.filter(
        Booking.check_in_date >= first_day,
        Booking.status.in_(['confirmed', 'checked_in', 'checked_out'])
    ).all()

    total = sum(booking.total_price for booking in monthly_bookings)
    return round(total, 2)