#!/usr/bin/env python3
"""
Hotel Management System Setup Script
نظام إدارة الفندق - سكريبت التثبيت
"""

import os
import sys
import subprocess

def install_requirements():
    """تثبيت المتطلبات"""
    requirements = [
        'Flask==2.3.3',
        'Flask-SQLAlchemy==3.0.5', 
        'Flask-Migrate==4.0.5',
        'Werkzeug==2.3.7',
        'Jinja2==3.1.2',
        'python-dotenv==1.0.0'
    ]
    
    print("🔧 تثبيت المتطلبات...")
    
    for requirement in requirements:
        try:
            print(f"📦 تثبيت {requirement}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', requirement])
            print(f"✅ تم تثبيت {requirement} بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {requirement}: {e}")
            return False
    
    return True

def create_database():
    """إنشاء قاعدة البيانات"""
    print("🗄️ إنشاء قاعدة البيانات...")
    
    try:
        # Import after installing requirements
        from app import create_app
        from app.models import db, User
        
        app = create_app()
        
        with app.app_context():
            # Create all tables
            db.create_all()
            
            # Create default admin user
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    role='admin'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
            
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            return True
            
    except Exception as e:
        print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        'app/static/uploads',
        'app/static/reports',
        'logs',
        'backups'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def main():
    """الدالة الرئيسية للتثبيت"""
    print("🏨 مرحباً بك في نظام إدارة الفندق")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        sys.exit(1)
    
    print(f"✅ إصدار Python: {sys.version}")
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        sys.exit(1)
    
    # Create database
    if not create_database():
        print("❌ فشل في إنشاء قاعدة البيانات")
        sys.exit(1)
    
    print("\n🎉 تم تثبيت النظام بنجاح!")
    print("=" * 50)
    print("📋 معلومات تسجيل الدخول الافتراضية:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n🚀 لتشغيل النظام:")
    print("   python run.py")
    print("\n🌐 ثم افتح المتصفح على:")
    print("   http://localhost:5000")

if __name__ == '__main__':
    main()
