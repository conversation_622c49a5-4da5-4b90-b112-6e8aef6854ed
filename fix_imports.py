#!/usr/bin/env python3
"""
Fix Flask imports and install required packages
حل مشاكل استيراد Flask وتثبيت المكتبات المطلوبة
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"🔄 Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Success: {command}")
            return True
        else:
            print(f"❌ Failed: {command}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Exception running {command}: {e}")
        return False

def install_packages():
    """Install required packages using different methods"""
    packages = [
        "Flask==2.3.3",
        "Flask-SQLAlchemy==3.0.5",
        "Flask-Migrate==4.0.5",
        "Werkzeug==2.3.7",
        "Jinja2==3.1.2"
    ]

    print("🏨 Hotel Management System - Package Installer")
    print("=" * 50)

    # Method 1: Try pip
    print("\n📦 Method 1: Using pip")
    success = True
    for package in packages:
        if not run_command(f"pip install {package}"):
            success = False
            break

    if success:
        print("✅ All packages installed successfully with pip!")
        return True

    # Method 2: Try python -m pip
    print("\n📦 Method 2: Using python -m pip")
    success = True
    for package in packages:
        if not run_command(f"python -m pip install {package}"):
            success = False
            break

    if success:
        print("✅ All packages installed successfully with python -m pip!")
        return True

    # Method 3: Try py -m pip (Windows)
    print("\n📦 Method 3: Using py -m pip (Windows)")
    success = True
    for package in packages:
        if not run_command(f"py -m pip install {package}"):
            success = False
            break

    if success:
        print("✅ All packages installed successfully with py -m pip!")
        return True

    # Method 4: Try with --user flag
    print("\n📦 Method 4: Using pip install --user")
    success = True
    for package in packages:
        if not run_command(f"pip install --user {package}"):
            success = False
            break

    if success:
        print("✅ All packages installed successfully with --user flag!")
        return True

    return False

def test_imports():
    """Test if imports work"""
    print("\n🧪 Testing imports...")

    try:
        import flask
        print(f"✅ Flask imported successfully - version: {flask.__version__}")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False

    try:
        import flask_sqlalchemy
        print(f"✅ Flask-SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"❌ Flask-SQLAlchemy import failed: {e}")
        return False

    try:
        from werkzeug.security import generate_password_hash
        print(f"✅ Werkzeug imported successfully")
    except ImportError as e:
        print(f"❌ Werkzeug import failed: {e}")
        return False

    return True

def create_simple_test():
    """Create a simple test file to verify Flask works"""
    test_code = '''
import sys
try:
    from flask import Flask
    from flask_sqlalchemy import SQLAlchemy

    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db = SQLAlchemy(app)

    print("✅ Flask and Flask-SQLAlchemy are working correctly!")
    print("🎉 You can now run the hotel management system!")

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please try running: pip install Flask Flask-SQLAlchemy")
    sys.exit(1)
'''

    with open('test_flask.py', 'w', encoding='utf-8') as f:
        f.write(test_code)

    print("\n🧪 Created test_flask.py - running test...")
    return run_command("python test_flask.py")

def main():
    """Main function"""
    print("🔧 Flask Import Fixer")
    print("=" * 30)

    # First, test if imports already work
    if test_imports():
        print("\n🎉 All imports are working! No installation needed.")
        return True

    # Try to install packages
    if install_packages():
        print("\n🧪 Testing imports after installation...")
        if test_imports():
            print("\n🎉 Installation successful! All imports working.")
            create_simple_test()
            return True

    print("\n❌ Could not install packages automatically.")
    print("\n🔧 Manual installation options:")
    print("1. Open Command Prompt as Administrator and run:")
    print("   pip install Flask Flask-SQLAlchemy")
    print("\n2. Or try:")
    print("   python -m pip install Flask Flask-SQLAlchemy")
    print("\n3. Or install Python from https://python.org and try again")
    print("\n4. Or use the simple version: python app_simple.py")

    return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
    else:
        print("\n✅ Ready to run hotel management system!")
        print("Run: python run.py")
        input("\nPress Enter to exit...")
