#!/usr/bin/env python3
"""
Simple installation script for Hotel Management System
سكريبت تثبيت مبسط لنظام إدارة الفندق
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package using pip"""
    try:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error installing {package}: {e}")
        return False

def main():
    """Main installation function"""
    print("🏨 Hotel Management System - Installation")
    print("=" * 50)
    
    # List of required packages
    packages = [
        "Flask==2.3.3",
        "Flask-SQLAlchemy==3.0.5",
        "Flask-Migrate==4.0.5",
        "Werkzeug==2.3.7",
        "Jinja2==3.1.2"
    ]
    
    print("📦 Installing required packages...")
    
    failed_packages = []
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        print("Please install them manually using:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    
    print("\n✅ All packages installed successfully!")
    
    # Create necessary directories
    print("\n📁 Creating directories...")
    directories = [
        "app/static/uploads",
        "app/static/reports", 
        "logs",
        "backups"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created: {directory}")
    
    print("\n🎉 Installation completed!")
    print("To run the application:")
    print("  python run.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
