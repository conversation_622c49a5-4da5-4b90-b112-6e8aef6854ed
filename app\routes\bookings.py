from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app.models import Booking, Room, Customer, db
from app.routes.auth import login_required
from datetime import datetime, date

bookings_bp = Blueprint('bookings', __name__)

@bookings_bp.route('/bookings')
@login_required
def manage_bookings():
    bookings = Booking.query.order_by(Booking.created_at.desc()).all()
    return render_template('bookings.html', bookings=bookings)

@bookings_bp.route('/bookings/add', methods=['GET', 'POST'])
@login_required
def add_booking():
    if request.method == 'POST':
        room_id = request.form.get('room_id')
        customer_id = request.form.get('customer_id')
        check_in_date = datetime.strptime(request.form.get('check_in_date'), '%Y-%m-%d').date()
        check_out_date = datetime.strptime(request.form.get('check_out_date'), '%Y-%m-%d').date()
        notes = request.form.get('notes', '')

        # Validation
        if check_in_date >= check_out_date:
            flash('تاريخ المغادرة يجب أن يكون بعد تاريخ الوصول!', 'danger')
            return redirect(url_for('bookings.add_booking'))

        if check_in_date < date.today():
            flash('تاريخ الوصول لا يمكن أن يكون في الماضي!', 'danger')
            return redirect(url_for('bookings.add_booking'))

        # Check room availability
        room = Room.query.get_or_404(room_id)
        if not is_room_available(room_id, check_in_date, check_out_date):
            flash('الغرفة غير متاحة في هذه التواريخ!', 'danger')
            return redirect(url_for('bookings.add_booking'))

        # Calculate total price
        nights = (check_out_date - check_in_date).days
        total_price = nights * room.price

        new_booking = Booking(
            room_id=room_id,
            customer_id=customer_id,
            check_in_date=check_in_date,
            check_out_date=check_out_date,
            total_price=total_price,
            notes=notes
        )

        db.session.add(new_booking)
        db.session.commit()
        flash('تم إنشاء الحجز بنجاح!', 'success')
        return redirect(url_for('bookings.manage_bookings'))

    rooms = Room.query.filter_by(is_available=True).all()
    customers = Customer.query.all()
    return render_template('add_booking.html', rooms=rooms, customers=customers)

@bookings_bp.route('/bookings/edit/<int:booking_id>', methods=['GET', 'POST'])
@login_required
def edit_booking(booking_id):
    booking = Booking.query.get_or_404(booking_id)

    if request.method == 'POST':
        booking.status = request.form.get('status')
        booking.notes = request.form.get('notes', '')

        db.session.commit()
        flash('تم تحديث الحجز بنجاح!', 'success')
        return redirect(url_for('bookings.manage_bookings'))

    return render_template('edit_booking.html', booking=booking)

@bookings_bp.route('/bookings/delete/<int:booking_id>', methods=['POST'])
@login_required
def delete_booking(booking_id):
    booking = Booking.query.get_or_404(booking_id)
    db.session.delete(booking)
    db.session.commit()
    flash('تم حذف الحجز بنجاح!', 'success')
    return redirect(url_for('bookings.manage_bookings'))

@bookings_bp.route('/bookings/check_availability', methods=['POST'])
@login_required
def check_availability():
    check_in_date = datetime.strptime(request.form.get('check_in_date'), '%Y-%m-%d').date()
    check_out_date = datetime.strptime(request.form.get('check_out_date'), '%Y-%m-%d').date()

    available_rooms = []
    for room in Room.query.filter_by(is_available=True).all():
        if is_room_available(room.id, check_in_date, check_out_date):
            available_rooms.append(room)

    return jsonify([room.to_dict() for room in available_rooms])

def is_room_available(room_id, check_in_date, check_out_date):
    """Check if a room is available for the given dates"""
    conflicting_bookings = Booking.query.filter(
        Booking.room_id == room_id,
        Booking.status.in_(['confirmed', 'checked_in']),
        Booking.check_in_date < check_out_date,
        Booking.check_out_date > check_in_date
    ).count()

    return conflicting_bookings == 0