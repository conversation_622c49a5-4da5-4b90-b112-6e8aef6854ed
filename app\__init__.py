from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import os

# Try to import Flask-Migrate, but don't fail if it's not available
try:
    from flask_migrate import Migrate
    MIGRATE_AVAILABLE = True
except ImportError:
    MIGRATE_AVAILABLE = False
    print("⚠️ Flask-Migrate not available. Database migrations disabled.")

def create_app(config_name=None):
    """Application factory pattern"""
    app = Flask(__name__)

    # Load configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    from config import config
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # Initialize extensions
    from app.models import db
    db.init_app(app)

    # Initialize Flask-Migrate if available
    if MIGRATE_AVAILABLE:
        migrate = Migrate(app, db)
        print("✅ Flask-Migrate initialized")
    else:
        print("⚠️ Flask-Migrate not available - using basic database creation")

    # Register blueprints
    from app.routes.auth import auth_bp
    from app.routes.rooms import rooms_bp
    from app.routes.bookings import bookings_bp
    from app.routes.customers import customers_bp
    from app.routes.dashboard import dashboard_bp
    from app.routes.services import services_bp
    from app.routes.maintenance import maintenance_bp
    from app.routes.reports import reports_bp
    from app.routes.notifications import notifications_bp
    from app.routes.reviews import reviews_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(rooms_bp)
    app.register_blueprint(bookings_bp)
    app.register_blueprint(customers_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(services_bp)
    app.register_blueprint(maintenance_bp)
    app.register_blueprint(reports_bp)
    app.register_blueprint(notifications_bp)
    app.register_blueprint(reviews_bp)

    # Create tables
    with app.app_context():
        db.create_all()

    return app