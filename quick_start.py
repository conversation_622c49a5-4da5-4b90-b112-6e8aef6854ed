#!/usr/bin/env python3
"""
Quick Start for Hotel Management System
بداية سريعة لنظام إدارة الفندق

This script will automatically install Flask and run the application
"""

import subprocess
import sys
import os

def print_header():
    """Print welcome header"""
    print("🏨" + "=" * 50)
    print("    Hotel Management System - Quick Start")
    print("    نظام إدارة الفندق - بداية سريعة")
    print("=" * 52)
    print()

def install_flask():
    """Install Flask and Flask-SQLAlchemy"""
    print("📦 Installing Flask and Flask-SQLAlchemy...")
    
    packages = ["Flask", "Flask-SQLAlchemy"]
    
    for package in packages:
        print(f"   Installing {package}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"   ✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"   ❌ Failed to install {package}")
            return False
        except Exception as e:
            print(f"   ❌ Error installing {package}: {e}")
            return False
    
    return True

def test_imports():
    """Test if Flask imports work"""
    print("\n🧪 Testing imports...")
    try:
        import flask
        import flask_sqlalchemy
        print("   ✅ All imports working!")
        return True
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False

def run_application():
    """Run the hotel management application"""
    print("\n🚀 Starting Hotel Management System...")
    print("🌐 Open your browser and go to: http://localhost:5000")
    print("👤 Login with: admin / admin123")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Try to run the simple version
        subprocess.run([sys.executable, "app_simple.py"])
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error running application: {e}")
        print("\nTrying alternative method...")
        try:
            subprocess.run([sys.executable, "run.py"])
        except Exception as e2:
            print(f"❌ Alternative method also failed: {e2}")
            return False
    
    return True

def main():
    """Main function"""
    print_header()
    
    # Check Python version
    if sys.version_info < (3, 6):
        print("❌ Python 3.6+ is required")
        print(f"   Current version: {sys.version}")
        input("Press Enter to exit...")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    
    # Install Flask
    if not install_flask():
        print("\n❌ Installation failed!")
        print("\n🔧 Manual installation:")
        print("   1. Open Command Prompt as Administrator")
        print("   2. Run: pip install Flask Flask-SQLAlchemy")
        print("   3. Then run: python app_simple.py")
        input("\nPress Enter to exit...")
        return False
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed!")
        print("Please try manual installation:")
        print("   pip install Flask Flask-SQLAlchemy")
        input("\nPress Enter to exit...")
        return False
    
    # Run application
    print("\n✅ Everything ready!")
    input("Press Enter to start the application...")
    
    return run_application()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
