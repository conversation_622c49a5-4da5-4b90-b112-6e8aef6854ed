# 🏨 نظام إدارة الفندق - Hotel Management System

نظام شامل لإدارة الفنادق مطور بلغة Python باستخدام إطار عمل Flask.

## ✨ الميزات الرئيسية

### 🏠 الميزات الأساسية
- **إدارة الغرف**: إضافة، تعديل، حذف الغرف مع تحديد الأنواع والأسعار
- **نظام الحجوزات**: إنشاء وإدارة الحجوزات مع التحقق من التوفر
- **إدارة العملاء**: تسجيل ومتابعة بيانات العملاء
- **لوحة التحكم**: عرض الإحصائيات والبيانات المهمة
- **نظام المصادقة**: تسجيل دخول آمن مع صلاحيات متعددة

### 🚀 الميزات المتقدمة
- **الخدمات الإضافية**: إدارة خدمات الفندق (طعام، سبا، نقل، ترفيه)
- **نظام الصيانة**: متابعة طلبات الصيانة والتنظيف
- **التقارير المفصلة**: تقارير مالية وإحصائيات الإشغال
- **نظام الإشعارات**: تنبيهات فورية للأحداث المهمة
- **التقييمات والمراجعات**: نظام تقييم العملاء للخدمات
- **المخططات البيانية**: رسوم بيانية تفاعلية للإحصائيات
- **التصدير والطباعة**: تصدير البيانات بصيغ مختلفة

## 🛠️ التقنيات المستخدمة

- **Backend**: Python 3.7+, Flask
- **Database**: SQLite (قابل للترقية لـ PostgreSQL)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Charts**: Chart.js
- **Icons**: Font Awesome

## Project Structure
```
hotel-management-app
├── app
│   ├── __init__.py
│   ├── models.py
│   ├── routes
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── rooms.py
│   │   ├── bookings.py
│   │   ├── customers.py
│   │   └── dashboard.py
│   ├── templates
│   │   ├── base.html
│   │   ├── login.html
│   │   ├── dashboard.html
│   │   ├── rooms.html
│   │   ├── bookings.html
│   │   └── customers.html
│   └── static
│       ├── css
│       │   └── style.css
│       └── js
│           └── main.js
├── config.py
├── requirements.txt
├── run.py
└── README.md
```

## 📋 المتطلبات

- Python 3.7 أو أحدث
- pip (مدير حزم Python)

## 🚀 التثبيت والتشغيل

### ⚡ البداية السريعة (الأسهل)

```bash
# تشغيل سريع مع تثبيت تلقائي
python quick_start.py
```

### الطريقة الأولى: التثبيت التلقائي

```bash
# 1. إصلاح مشاكل الاستيراد وتثبيت المكتبات
python fix_imports.py

# 2. تشغيل التطبيق
python run.py
```

### الطريقة الثانية: النسخة المبسطة (إذا فشلت الطرق الأخرى)

```bash
# تشغيل النسخة المبسطة مباشرة
python app_simple.py
```

### الطريقة الثالثة: التثبيت اليدوي

```bash
# 1. تثبيت المكتبات الأساسية
pip install Flask Flask-SQLAlchemy

# 2. تشغيل التطبيق
python run.py
```

### الطريقة الرابعة: استخدام البيئة الافتراضية (مُوصى به للمطورين)

```bash
# 1. إنشاء بيئة افتراضية
python -m venv hotel_env

# 2. تفعيل البيئة الافتراضية
# Windows:
hotel_env\Scripts\activate
# Linux/Mac:
source hotel_env/bin/activate

# 3. تثبيت المكتبات
pip install Flask Flask-SQLAlchemy

# 4. تشغيل التطبيق
python app_simple.py
```

## 🌐 الوصول للنظام

بعد تشغيل التطبيق، افتح المتصفح على:
```
http://localhost:5000
```

### بيانات تسجيل الدخول الافتراضية:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 🔧 حل مشاكل الاستيراد

### ❌ المشاكل الشائعة:
```
Import "flask" could not be resolved
Import "flask_sqlalchemy" could not be resolved
Import "flask_migrate" could not be resolved from source
Python was not found
```

### ✅ الحلول السريعة:

#### 🚀 الحل الأسرع (للمبتدئين):
```bash
# انقر نقراً مزدوجاً على:
start.bat

# أو في PowerShell:
.\start.bat
```

#### 🔧 الحل اليدوي:
```bash
# 1. تثبيت Python من Microsoft Store أو python.org
# 2. تثبيت Flask
python -m pip install Flask Flask-SQLAlchemy

# 3. تشغيل النظام
python app_simple.py
```

#### 📋 حلول بديلة:
```bash
# الحل الأول: سكريبت التثبيت
install_flask.bat

# الحل الثاني: النسخة المستقلة
python models_standalone.py

# الحل الثالث: دليل مفصل
# اقرأ SETUP_GUIDE.md
```

### 🆘 إذا استمرت المشاكل:

1. **اقرأ الدليل الشامل:** [SETUP_GUIDE.md](SETUP_GUIDE.md)
2. **راجع حل المشاكل:** [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
3. **تأكد من تثبيت Python بشكل صحيح**
4. **أعد تشغيل الكمبيوتر بعد تثبيت Python**

## 📊 التقارير والإحصائيات

### التقرير المالي
- إجمالي الإيرادات
- الإيرادات حسب طريقة الدفع
- الإيرادات اليومية والشهرية
- مخططات بيانية تفاعلية

### تقرير الإشغال
- معدل إشغال الغرف
- الإشغال حسب نوع الغرفة
- توقعات الإشغال

## 🔔 نظام الإشعارات

- إشعارات الحجوزات الجديدة
- تذكيرات الوصول والمغادرة
- تنبيهات الصيانة العاجلة
- ملخصات دورية

## 🛡️ الأمان

- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- صلاحيات متدرجة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**تم تطوير هذا النظام بـ ❤️ لخدمة صناعة الضيافة**