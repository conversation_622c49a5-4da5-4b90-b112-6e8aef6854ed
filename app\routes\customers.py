from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash
from app.models import Customer, db
from app.routes.auth import login_required

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/customers')
@login_required
def view_customers():
    customers = Customer.query.order_by(Customer.created_at.desc()).all()
    return render_template('customers.html', customers=customers)

@customers_bp.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        phone = request.form['phone']
        address = request.form.get('address', '')

        # Check if email already exists
        if Customer.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل!', 'danger')
            return render_template('add_customer.html')

        new_customer = Customer(
            name=name,
            email=email,
            phone=phone,
            address=address
        )
        db.session.add(new_customer)
        db.session.commit()
        flash('تم إضافة العميل بنجاح!', 'success')
        return redirect(url_for('customers.view_customers'))

    return render_template('add_customer.html')

@customers_bp.route('/customers/edit/<int:customer_id>', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        customer.name = request.form['name']
        customer.email = request.form['email']
        customer.phone = request.form['phone']
        customer.address = request.form.get('address', '')

        db.session.commit()
        flash('تم تحديث بيانات العميل بنجاح!', 'success')
        return redirect(url_for('customers.view_customers'))

    return render_template('edit_customer.html', customer=customer)

@customers_bp.route('/customers/delete/<int:customer_id>', methods=['POST'])
@login_required
def delete_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)

    # Check if customer has active bookings
    if customer.bookings:
        flash('لا يمكن حذف العميل لأنه يحتوي على حجوزات!', 'danger')
        return redirect(url_for('customers.view_customers'))

    db.session.delete(customer)
    db.session.commit()
    flash('تم حذف العميل بنجاح!', 'success')
    return redirect(url_for('customers.view_customers'))

@customers_bp.route('/customers/<int:customer_id>/bookings')
@login_required
def customer_bookings(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    return render_template('customer_bookings.html', customer=customer)

# API endpoints for AJAX requests
@customers_bp.route('/api/customers')
@login_required
def api_get_customers():
    customers = Customer.query.all()
    return jsonify([customer.to_dict() for customer in customers])

@customers_bp.route('/api/customers/<int:customer_id>')
@login_required
def api_get_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    return jsonify(customer.to_dict())