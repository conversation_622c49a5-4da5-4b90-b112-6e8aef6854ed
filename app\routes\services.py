from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app.models import Service, BookingService, Booking, db
from app.routes.auth import login_required

services_bp = Blueprint('services', __name__)

@services_bp.route('/services')
@login_required
def view_services():
    """عرض جميع الخدمات"""
    services = Service.query.filter_by(is_active=True).all()
    return render_template('services/list.html', services=services)

@services_bp.route('/services/add', methods=['GET', 'POST'])
@login_required
def add_service():
    """إضافة خدمة جديدة"""
    if request.method == 'POST':
        name = request.form['name']
        description = request.form.get('description', '')
        price = float(request.form['price'])
        category = request.form['category']
        
        new_service = Service(
            name=name,
            description=description,
            price=price,
            category=category
        )
        
        db.session.add(new_service)
        db.session.commit()
        flash('تم إضافة الخدمة بنجاح!', 'success')
        return redirect(url_for('services.view_services'))
    
    return render_template('services/add.html')

@services_bp.route('/services/edit/<int:service_id>', methods=['GET', 'POST'])
@login_required
def edit_service(service_id):
    """تعديل خدمة"""
    service = Service.query.get_or_404(service_id)
    
    if request.method == 'POST':
        service.name = request.form['name']
        service.description = request.form.get('description', '')
        service.price = float(request.form['price'])
        service.category = request.form['category']
        service.is_active = 'is_active' in request.form
        
        db.session.commit()
        flash('تم تحديث الخدمة بنجاح!', 'success')
        return redirect(url_for('services.view_services'))
    
    return render_template('services/edit.html', service=service)

@services_bp.route('/services/delete/<int:service_id>', methods=['POST'])
@login_required
def delete_service(service_id):
    """حذف خدمة"""
    service = Service.query.get_or_404(service_id)
    service.is_active = False  # Soft delete
    db.session.commit()
    flash('تم حذف الخدمة بنجاح!', 'success')
    return redirect(url_for('services.view_services'))

@services_bp.route('/services/booking/<int:booking_id>')
@login_required
def booking_services(booking_id):
    """عرض خدمات الحجز"""
    booking = Booking.query.get_or_404(booking_id)
    available_services = Service.query.filter_by(is_active=True).all()
    booking_services = BookingService.query.filter_by(booking_id=booking_id).all()
    
    return render_template('services/booking_services.html', 
                         booking=booking, 
                         available_services=available_services,
                         booking_services=booking_services)

@services_bp.route('/services/add_to_booking', methods=['POST'])
@login_required
def add_service_to_booking():
    """إضافة خدمة إلى حجز"""
    booking_id = request.form['booking_id']
    service_id = request.form['service_id']
    quantity = int(request.form.get('quantity', 1))
    
    service = Service.query.get_or_404(service_id)
    total_price = service.price * quantity
    
    # Check if service already exists for this booking
    existing = BookingService.query.filter_by(
        booking_id=booking_id, 
        service_id=service_id
    ).first()
    
    if existing:
        existing.quantity += quantity
        existing.total_price += total_price
    else:
        booking_service = BookingService(
            booking_id=booking_id,
            service_id=service_id,
            quantity=quantity,
            total_price=total_price
        )
        db.session.add(booking_service)
    
    db.session.commit()
    flash('تم إضافة الخدمة إلى الحجز بنجاح!', 'success')
    return redirect(url_for('services.booking_services', booking_id=booking_id))

@services_bp.route('/services/remove_from_booking/<int:booking_service_id>', methods=['POST'])
@login_required
def remove_service_from_booking(booking_service_id):
    """إزالة خدمة من حجز"""
    booking_service = BookingService.query.get_or_404(booking_service_id)
    booking_id = booking_service.booking_id
    
    db.session.delete(booking_service)
    db.session.commit()
    flash('تم إزالة الخدمة من الحجز بنجاح!', 'success')
    return redirect(url_for('services.booking_services', booking_id=booking_id))

@services_bp.route('/api/services/category/<category>')
@login_required
def api_services_by_category(category):
    """API للحصول على الخدمات حسب الفئة"""
    services = Service.query.filter_by(category=category, is_active=True).all()
    return jsonify([service.to_dict() for service in services])

@services_bp.route('/api/services/search')
@login_required
def api_search_services():
    """API للبحث في الخدمات"""
    query = request.args.get('q', '')
    services = Service.query.filter(
        Service.name.contains(query),
        Service.is_active == True
    ).all()
    return jsonify([service.to_dict() for service in services])
