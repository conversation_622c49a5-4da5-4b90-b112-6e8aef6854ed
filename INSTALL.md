# 🛠️ دليل التثبيت - Hotel Management System

## 📋 المتطلبات الأساسية

- **Python 3.7+** (مطلوب)
- **pip** (مدير حزم Python)
- **متصفح ويب** حديث

## 🚀 طرق التثبيت

### الطريقة الأولى: التثبيت التلقائي (الأسهل)

```bash
# 1. تشغيل سكريبت التثبيت
python install.py

# 2. تشغيل التطبيق
python run.py
```

### الطريقة الثانية: التثبيت اليدوي

```bash
# 1. تثبيت المكتبات المطلوبة
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-Migrate==4.0.5
pip install Werkzeug==2.3.7

# 2. تشغيل التطبيق
python run.py
```

### الطريقة الثالثة: النسخة المبسطة (إذا فشلت الطرق الأخرى)

```bash
# تشغيل النسخة المبسطة مباشرة
python app_simple.py
```

## 🔧 حل مشاكل التثبيت الشائعة

### مشكلة: "Import flask could not be resolved"

**الحل:**
```bash
# تثبيت Flask
pip install Flask

# أو استخدام python -m pip
python -m pip install Flask
```

### مشكلة: "pip is not recognized"

**الحل:**
```bash
# استخدام python -m pip بدلاً من pip
python -m pip install Flask Flask-SQLAlchemy

# أو تحديد مسار Python كاملاً
C:\Python39\python.exe -m pip install Flask
```

### مشكلة: "Permission denied"

**الحل:**
```bash
# تثبيت للمستخدم الحالي فقط
pip install --user Flask Flask-SQLAlchemy

# أو تشغيل كمدير (Windows)
# افتح Command Prompt كمدير ثم نفذ:
pip install Flask Flask-SQLAlchemy
```

### مشكلة: "No module named 'flask_sqlalchemy'"

**الحل:**
```bash
# تثبيت Flask-SQLAlchemy
pip install Flask-SQLAlchemy

# التحقق من التثبيت
python -c "import flask_sqlalchemy; print('OK')"
```

## 🐍 التحقق من إصدار Python

```bash
# التحقق من إصدار Python
python --version

# إذا لم يعمل، جرب:
python3 --version
py --version
```

**ملاحظة:** يجب أن يكون الإصدار 3.7 أو أحدث.

## 📦 التحقق من تثبيت المكتبات

```bash
# التحقق من Flask
python -c "import flask; print('Flask version:', flask.__version__)"

# التحقق من Flask-SQLAlchemy
python -c "import flask_sqlalchemy; print('Flask-SQLAlchemy OK')"

# عرض جميع المكتبات المثبتة
pip list
```

## 🌐 تشغيل التطبيق

بعد التثبيت الناجح:

```bash
# تشغيل التطبيق الكامل
python run.py

# أو تشغيل النسخة المبسطة
python app_simple.py
```

ثم افتح المتصفح على: `http://localhost:5000`

## 👤 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 🔄 إعادة تثبيت المكتبات

إذا واجهت مشاكل، يمكنك إعادة تثبيت المكتبات:

```bash
# إلغاء تثبيت المكتبات
pip uninstall Flask Flask-SQLAlchemy Flask-Migrate

# إعادة التثبيت
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5 Flask-Migrate==4.0.5
```

## 🐳 استخدام البيئة الافتراضية (مُوصى به)

```bash
# إنشاء بيئة افتراضية
python -m venv hotel_env

# تفعيل البيئة الافتراضية
# Windows:
hotel_env\Scripts\activate
# Linux/Mac:
source hotel_env/bin/activate

# تثبيت المكتبات في البيئة الافتراضية
pip install Flask Flask-SQLAlchemy Flask-Migrate

# تشغيل التطبيق
python run.py

# إلغاء تفعيل البيئة الافتراضية
deactivate
```

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **تحقق من إصدار Python:** يجب أن يكون 3.7+
2. **جرب النسخة المبسطة:** `python app_simple.py`
3. **استخدم البيئة الافتراضية:** كما هو موضح أعلاه
4. **تحقق من اتصال الإنترنت:** لتحميل المكتبات

## 📱 اختبار التطبيق

بعد التشغيل الناجح:

1. افتح `http://localhost:5000`
2. سجل دخول بـ `admin` / `admin123`
3. تصفح لوحة التحكم
4. جرب إضافة غرفة جديدة

## 🔧 ملفات مساعدة

- `install.py` - سكريبت التثبيت التلقائي
- `app_simple.py` - نسخة مبسطة من التطبيق
- `run.py` - ملف التشغيل الرئيسي
- `requirements.txt` - قائمة المكتبات المطلوبة

---

**💡 نصيحة:** ابدأ بـ `python app_simple.py` للتأكد من عمل Python وFlask، ثم انتقل للنسخة الكاملة.
