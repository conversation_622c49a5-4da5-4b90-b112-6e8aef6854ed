#!/usr/bin/env python3
"""
Hotel Management System - Main Runner
نظام إدارة الفندق - ملف التشغيل الرئيسي
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """Check if required dependencies are installed"""
    missing_deps = []

    try:
        import flask
    except ImportError:
        missing_deps.append('Flask')

    try:
        import flask_sqlalchemy
    except ImportError:
        missing_deps.append('Flask-SQLAlchemy')

    return missing_deps

def main():
    """Main function to run the application"""
    print("🏨 Hotel Management System")
    print("=" * 40)

    # Check dependencies
    missing = check_dependencies()
    if missing:
        print("❌ Missing required dependencies:")
        for dep in missing:
            print(f"   - {dep}")
        print("\n🔧 To install dependencies, choose one option:")
        print("   1. Run: python install.py")
        print("   2. Run: pip install Flask Flask-SQLAlchemy")
        print("   3. Run the simple version: python app_simple.py")
        return False

    # Try to import and run the full application
    try:
        from app import create_app

        app = create_app()

        print("✅ All dependencies found")
        print("🚀 Starting Hotel Management System...")
        print("🌐 Open your browser and go to: http://localhost:5000")
        print("👤 Default login: admin / admin123")
        print("🛑 Press Ctrl+C to stop the server")
        print("-" * 40)

        app.run(debug=True, host='0.0.0.0', port=5000)

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n🔄 Trying simple version...")

        # Try to run the simple version
        try:
            import subprocess
            subprocess.run([sys.executable, 'app_simple.py'])
        except Exception as simple_error:
            print(f"❌ Simple version also failed: {simple_error}")
            print("\n📋 Manual installation required:")
            print("   pip install Flask Flask-SQLAlchemy")
            return False

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)