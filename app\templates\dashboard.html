{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة التحكم
        </h1>
        <div class="text-muted">
            مرحباً، {{ username }}
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-container mb-4">
        <div class="stat-card rooms">
            <div class="stat-icon">
                <i class="fas fa-bed"></i>
            </div>
            <div class="stat-number">{{ total_rooms }}</div>
            <div class="stat-label">إجمالي الغرف</div>
        </div>

        <div class="stat-card bookings">
            <div class="stat-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-number">{{ available_rooms }}</div>
            <div class="stat-label">الغرف المتاحة</div>
        </div>

        <div class="stat-card customers">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number">{{ booked_rooms }}</div>
            <div class="stat-label">الغرف المحجوزة</div>
        </div>

        <div class="stat-card income">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-number">{{ daily_income }}</div>
            <div class="stat-label">الدخل اليومي (ريال)</div>
        </div>

        <div class="stat-card income">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-number">{{ monthly_income }}</div>
            <div class="stat-label">الدخل الشهري (ريال)</div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Bookings -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أحدث الحجوزات
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الحجز</th>
                                        <th>العميل</th>
                                        <th>الغرفة</th>
                                        <th>تاريخ الوصول</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in recent_bookings %}
                                    <tr>
                                        <td>#{{ booking.id }}</td>
                                        <td>{{ booking.customer.name }}</td>
                                        <td>{{ booking.room.room_number }}</td>
                                        <td>{{ booking.check_in_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <span class="status-badge status-{{ booking.status }}">
                                                {{ booking.status }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <p>لا توجد حجوزات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Room Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات الغرف
                    </h5>
                </div>
                <div class="card-body">
                    {% if room_stats %}
                        {% for stat in room_stats %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>
                                    {% if stat.room_type == 'single' %}
                                        غرف مفردة
                                    {% elif stat.room_type == 'double' %}
                                        غرف مزدوجة
                                    {% elif stat.room_type == 'suite' %}
                                        أجنحة
                                    {% else %}
                                        {{ stat.room_type }}
                                    {% endif %}
                                </strong>
                                <br>
                                <small class="text-muted">
                                    {{ stat.available or 0 }} متاحة من {{ stat.total }}
                                </small>
                            </div>
                            <div class="text-end">
                                <div class="progress" style="width: 100px; height: 8px;">
                                    <div class="progress-bar"
                                         style="width: {{ ((stat.available or 0) / stat.total * 100) if stat.total > 0 else 0 }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-bed fa-3x mb-3"></i>
                            <p>لا توجد غرف مسجلة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('rooms.add_room') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة غرفة جديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('bookings.add_booking') }}" class="btn btn-success w-100">
                                <i class="fas fa-calendar-plus me-2"></i>
                                حجز جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('customers.add_customer') }}" class="btn btn-warning w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                عميل جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('rooms.view_rooms') }}" class="btn btn-info w-100">
                                <i class="fas fa-list me-2"></i>
                                عرض جميع الغرف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}