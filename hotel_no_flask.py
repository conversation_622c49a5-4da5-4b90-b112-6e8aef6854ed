#!/usr/bin/env python3
"""
Hotel Management System - No Flask Version
نظام إدارة الفندق - نسخة بدون Flask

This version works without Flask for demonstration purposes
"""

import http.server
import socketserver
import json
import urllib.parse
from models_standalone import DatabaseManager, User, Room, Customer, Booking

class HotelHTTPHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP handler for hotel management"""
    
    def __init__(self, *args, **kwargs):
        # Initialize database
        self.db = DatabaseManager()
        self.user_model = User(self.db)
        self.room_model = Room(self.db)
        self.customer_model = Customer(self.db)
        self.booking_model = Booking(self.db)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_dashboard()
        elif self.path == '/login':
            self.serve_login()
        elif self.path == '/rooms':
            self.serve_rooms()
        elif self.path == '/customers':
            self.serve_customers()
        elif self.path == '/bookings':
            self.serve_bookings()
        elif self.path.startswith('/api/'):
            self.handle_api_get()
        else:
            super().do_GET()
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path.startswith('/api/'):
            self.handle_api_post()
        else:
            self.send_error(404)
    
    def serve_dashboard(self):
        """Serve dashboard page"""
        stats = self.room_model.get_stats()
        recent_bookings = self.booking_model.get_all()[:5]
        
        html = f'''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {{ background: linear-gradient(135deg, #2c3e50, #3498db); min-height: 100vh; }}
        .container {{ background: white; margin: 20px auto; padding: 30px; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }}
        .stat-card {{ background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }}
        .stat-icon {{ font-size: 2rem; margin-bottom: 10px; }}
        .stat-number {{ font-size: 1.8rem; font-weight: bold; margin-bottom: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">
                <i class="fas fa-hotel me-3"></i>
                نظام إدارة الفندق
            </h1>
            <p class="lead text-muted">نسخة تجريبية بدون Flask</p>
        </div>
        
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <div class="navbar-nav">
                    <a class="nav-link active" href="/">لوحة التحكم</a>
                    <a class="nav-link" href="/rooms">الغرف</a>
                    <a class="nav-link" href="/customers">العملاء</a>
                    <a class="nav-link" href="/bookings">الحجوزات</a>
                </div>
            </div>
        </nav>
        
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon text-primary"><i class="fas fa-bed"></i></div>
                    <div class="stat-number">{stats['total_rooms']}</div>
                    <div class="text-muted">إجمالي الغرف</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon text-success"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-number">{stats['available_rooms']}</div>
                    <div class="text-muted">الغرف المتاحة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon text-warning"><i class="fas fa-calendar-check"></i></div>
                    <div class="stat-number">{stats['booked_rooms']}</div>
                    <div class="text-muted">الغرف المحجوزة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon text-info"><i class="fas fa-dollar-sign"></i></div>
                    <div class="stat-number">15,750</div>
                    <div class="text-muted">الإيرادات (ريال)</div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
            <p class="mb-0">هذه نسخة تجريبية تعمل بدون Flask. لتشغيل النسخة الكاملة، يرجى تثبيت Flask أولاً.</p>
        </div>
        
        <div class="text-center mt-4">
            <h6>🔧 لتثبيت Flask:</h6>
            <code>python -m pip install Flask Flask-SQLAlchemy</code>
        </div>
    </div>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_rooms(self):
        """Serve rooms page"""
        rooms = self.room_model.get_all()
        
        rooms_html = ""
        for room in rooms:
            status = "متاحة" if room['is_available'] else "محجوزة"
            status_class = "success" if room['is_available'] else "danger"
            rooms_html += f'''
                <tr>
                    <td>{room['room_number']}</td>
                    <td>{room['room_type']}</td>
                    <td>{room['price']} ريال</td>
                    <td><span class="badge bg-{status_class}">{status}</span></td>
                </tr>
            '''
        
        html = f'''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الغرف - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {{ background: linear-gradient(135deg, #2c3e50, #3498db); min-height: 100vh; }}
        .container {{ background: white; margin: 20px auto; padding: 30px; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }}
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>إدارة الغرف</h1>
            <a href="/" class="btn btn-secondary">العودة للوحة التحكم</a>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الغرفة</th>
                        <th>نوع الغرفة</th>
                        <th>السعر</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {rooms_html}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_customers(self):
        """Serve customers page"""
        customers = self.customer_model.get_all()
        
        customers_html = ""
        for customer in customers:
            customers_html += f'''
                <tr>
                    <td>{customer['name']}</td>
                    <td>{customer['email']}</td>
                    <td>{customer['phone']}</td>
                    <td>{customer['address'] or 'غير محدد'}</td>
                </tr>
            '''
        
        html = f'''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العملاء - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {{ background: linear-gradient(135deg, #2c3e50, #3498db); min-height: 100vh; }}
        .container {{ background: white; margin: 20px auto; padding: 30px; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }}
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>إدارة العملاء</h1>
            <a href="/" class="btn btn-secondary">العودة للوحة التحكم</a>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>العنوان</th>
                    </tr>
                </thead>
                <tbody>
                    {customers_html}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_bookings(self):
        """Serve bookings page"""
        bookings = self.booking_model.get_all()
        
        bookings_html = ""
        for booking in bookings:
            bookings_html += f'''
                <tr>
                    <td>{booking['room_number']}</td>
                    <td>{booking['customer_name']}</td>
                    <td>{booking['check_in_date']}</td>
                    <td>{booking['check_out_date']}</td>
                    <td>{booking['total_price']} ريال</td>
                    <td><span class="badge bg-success">{booking['status']}</span></td>
                </tr>
            '''
        
        html = f'''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحجوزات - نظام إدارة الفندق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {{ background: linear-gradient(135deg, #2c3e50, #3498db); min-height: 100vh; }}
        .container {{ background: white; margin: 20px auto; padding: 30px; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }}
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>إدارة الحجوزات</h1>
            <a href="/" class="btn btn-secondary">العودة للوحة التحكم</a>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الغرفة</th>
                        <th>اسم العميل</th>
                        <th>تاريخ الوصول</th>
                        <th>تاريخ المغادرة</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {bookings_html}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def handle_api_get(self):
        """Handle API GET requests"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({"status": "API working"}).encode())
    
    def handle_api_post(self):
        """Handle API POST requests"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({"status": "POST received"}).encode())

def run_server(port=8000):
    """Run the hotel management server"""
    print("🏨 Hotel Management System - No Flask Version")
    print("=" * 50)
    print(f"🚀 Starting server on port {port}...")
    print(f"🌐 Open your browser and go to: http://localhost:{port}")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        with socketserver.TCPServer(("", port), HotelHTTPHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")

if __name__ == "__main__":
    run_server()
